<!--
 * @Author: <PERSON>
 * @Date: 2024-08-27 17:57:38
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-01-16 10:43:39
 * @FilePath: \sc-a-g03\更新日志.md
 * @Description: 
 * 
-->
# 1.0.2
1、修复断网不使用安全重启的bug
2、修复1.0.1版本中遥控器数据没有同步的bug


# V1.0.1
## 内容
1. 修复上报执行结果错误的bug
2. 新增从云端获取电量电量值、模式、设定温度、风速、使用时间，使其连续，提高用户体验
3. 若红外执行失败，则会重复执行一遍，返回第二次执行的结果
## 目标
1. 云端获取电量值、模式、设定温度、风速，减少电量损失和控制误会，提高用户体验
2. 云端命令需要给一个正确的响应




# V1.0.0.2
1、去掉了很多注释
2、去掉了不必要的函数
3、去掉了可以读取所有参数的AT指令


# V1.0.0
## bug
1、不能及时响应命令
2、imei上传不了

## 优化点
1、需要上传模块号，否则不好通过合宙升级
1、蓝牙模块名称修改