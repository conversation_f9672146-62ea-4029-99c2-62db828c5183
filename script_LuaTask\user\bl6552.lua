
--[[
Description: 
Version: V1.0.0
Autor: <PERSON>
Date: 2024-07-20 09:39:03
LastEditors: <PERSON>
LastEditTime: 2024-07-20 09:39:03
--]]

local sys = require "sys"
local UART_BL6552 = 1

local frist_init = 0
local bl6552_busy = 0 --0为不忙，1为忙
local bl6552_reflash_time = 10000 

local buff_len = 2048

local bl6552_data = {
            ia = 0.0,			--a相电流
            ib = 0.0,			--b相电流
            ic = 0.0,			--c相电流

            ua = 0.0,			--a相电压
            ub = 0.0,			--b相电压
            uc = 0.0,			--c相电压

            pa = 0.0,			--a相有功功率
            pb = 0.0,			--b相有功功率
            pc = 0.0,			--c相有功功率
            p = 0.0,			--合相有功功率

            -- pa_app = 0.0,		--相视在功率
            -- pb_app = 0.0,		--相视在功率
            -- pc_app = 0.0,		--相视在功率
            -- p_app = 0.0,		--视在功率

            -- pa_rev = 0.0,		--相无功功率
            -- pb_rev = 0.0,		--相无功功率
            -- pc_rev = 0.0,		--相无功功率
            -- p_rev = 0.0,		--无功功率
      
            -- ep_a_app = 0.0,			    --a相有功电量，
            -- ep_b_app = 0.0,			    --b相有功电量，
            -- ep_c_app = 0.0,			    --c相有功电量，
            -- ep_app = 0.0,               --合相有功电量，
            

            ep_a = 0.0,			        --a相正向有功电量，
            ep_b = 0.0,			        --b相正向有功电量，
            ep_c = 0.0,			        --c相正向有功电量，
            ep = 0.0,                   --合相正向有功电量，
            

            -- ep_a_rev = 0.0,			    --a相反向有功电量，
            -- ep_b_rev = 0.0,			    --b相反向有功电量，
            -- ep_c_rev = 0.0,			    --c相反向有功电量，
            -- ep_rev = 0.0,               --合相反向有功电量，
            

            -- nep_a_app = 0.0,			--a相无功电量，
            -- nep_b_app = 0.0,			--b相无功电量，
            -- nep_c_app = 0.0,			--c相无功电量，
            -- nep_app = 0.0,              --合相无功电量，
           
            -- ep_init_a_app = 0.0,	    --a相有功电量，
            -- ep_init_b_app = 0.0,	    --b相有功电量，
            -- ep_init_c_app = 0.0,	    --c相有功电量，
            -- ep_init_app = 0.0,          --合相有功初始电量，

            ep_init_a = 0.0,	        --a相正向有功初始电量，
            ep_init_b = 0.0,	        --b相正向有功初始电量，
            ep_init_c = 0.0,	        --c相正向有功初始电量，
            ep_init = 0.0,              --合相正向有功初始电量，

            -- ep_init_a_rev = 0.0,		--a相反向有功电量，
            -- ep_init_b_rev = 0.0,		--b相反向有功电量，
            -- ep_init_c_rev = 0.0,		--c相反向有功电量，
            -- ep_init_rev = 0.0,          --合相反向有功初始电量，

            -- nep_init_a_app = 0.0,		--a相无功电量，
            -- nep_init_b_app = 0.0,		--b相无功电量，
            -- nep_init_c_app = 0.0,		--c相无功电量，
            -- nep_init_app = 0.0,         --合相无功初始电量，

            pf_a=0.0,			        --a相功率因数
            pf_b=0.0,			        --b相功率因数
            pf_c=0.0,			        --c相功率因数
            pf = 0.0,                   --合相功率因数

            freq = 0.0,			        --频率

            phase_loss = 0x00,          --缺相告警 bit0 A相，bit1 B相，bit2 C相  1为缺相
            phase_loss_time = 0,        --缺相发生时的时间戳，正常时为0

            -- factor_ia = 1,
            -- factor_ib = 1,
            -- factor_ic = 1,

            -- factor_ua = 1,
            -- factor_ub = 1,
            -- factor_uc = 1,

            factor_pa = 1,
            factor_pb = 1,
            factor_pc = 1,
            factor_p_sum = 1,

            -- ep_change_save_min = 1          -- ep 变化存储的颗粒度
            }  

local BL6552_SUCCESS = 0
local BL6552_FAIL = -1


--防潜动功率1w
local p_var_wa_creep   = 0x001001


local Ki = 59856.4
local Ku = 11998.2
local Kp = 171.27
-- local CA = 4486.06
local Kcf = 0.000223
local Kp_sum = Kp/4
-- local CA_sum = CA/4
local Kcf_sum = Kcf*4



local BL_UART_RX_DONE = "BL_UART_RX_DONE"
local bl_data_queue = {}
local bl_writeBuff = {}
local bl_writeBusy = false


local BL6552_ADDR_READ = 0x35
local BL6552_ADDR_WRITE = 0xCA

local phase_loss_a_pin = 22
local phase_loss_b_pin = 24
local phase_loss_c_pin = 26

local debug = 0

local bl6552_uart_pram = {
    baudrate = 9600,
    dataBits = 8,
    parity = uart.NONE,
    stopBits = 1
}

local bl6552_reg_all = {
	-- reg_ic_wave            =	{addr = 0x02, len = 3, val_bits = 24, val = 0  },
	-- reg_ib_wave            =	{addr = 0x03, len = 3, val_bits = 24, val = 0  },
	-- reg_ia_wave            =	{addr = 0x04, len = 3, val_bits = 24, val = 0  },
	-- reg_in_wave            =	{addr = 0x05, len = 3, val_bits = 24, val = 0  },
	-- reg_va_wave            =	{addr = 0x08, len = 3, val_bits = 24, val = 0  },
	-- reg_vb_wave            =	{addr = 0x09, len = 3, val_bits = 24, val = 0  },
	-- reg_vc_wave            =	{addr = 0x0A, len = 3, val_bits = 24, val = 0  },
	reg_ic_rms             =	{addr = 0x0D, len = 3, val_bits = 24, val = 0  },
	reg_ib_rms             =	{addr = 0x0E, len = 3, val_bits = 24, val = 0  },
	reg_ia_rms             =	{addr = 0x0F, len = 3, val_bits = 24, val = 0  },
	reg_in_rms             =	{addr = 0x10, len = 3, val_bits = 24, val = 0  },
	reg_va_rms             =	{addr = 0x13, len = 3, val_bits = 24, val = 0  },
	reg_vb_rms             =	{addr = 0x14, len = 3, val_bits = 24, val = 0  },
	reg_vc_rms             =	{addr = 0x15, len = 3, val_bits = 24, val = 0  },
	-- reg_ic_fast_rms        =	{addr = 0x18, len = 3, val_bits = 24, val = 0  },
	-- reg_ib_fast_rms        =	{addr = 0x19, len = 3, val_bits = 24, val = 0  },
	-- reg_ia_fast_rms        =	{addr = 0x1A, len = 3, val_bits = 24, val = 0  },
	-- reg_in_fast_rms        =	{addr = 0x1B, len = 3, val_bits = 24, val = 0  },
	-- reg_va_fast_rms        =	{addr = 0x1E, len = 3, val_bits = 24, val = 0  },
	-- reg_vb_fast_rms        =	{addr = 0x1F, len = 3, val_bits = 24, val = 0  },
	-- reg_vc_fast_rms        =	{addr = 0x20, len = 3, val_bits = 24, val = 0  },
	reg_watt_a             =	{addr = 0x22, len = 3, val_bits = 24, val = 0  },
	reg_watt_b             =	{addr = 0x23, len = 3, val_bits = 24, val = 0  },
	reg_watt_c             =	{addr = 0x24, len = 3, val_bits = 24, val = 0  },
	reg_watt               =	{addr = 0x25, len = 3, val_bits = 24, val = 0  },
	-- reg_va_a               =	{addr = 0x26, len = 3, val_bits = 24, val = 0  },
	-- reg_va_b               =	{addr = 0x27, len = 3, val_bits = 24, val = 0  },
	-- reg_va_c               =	{addr = 0x28, len = 3, val_bits = 24, val = 0  },
	-- reg_va                 =	{addr = 0x29, len = 3, val_bits = 24, val = 0  },
	reg_fvar_a             =	{addr = 0x2A, len = 3, val_bits = 24, val = 0  },
	reg_fvar_b             =	{addr = 0x2B, len = 3, val_bits = 24, val = 0  },
	reg_fvar_c             =	{addr = 0x2C, len = 3, val_bits = 24, val = 0  },
	reg_fvar               =	{addr = 0x2D, len = 3, val_bits = 24, val = 0  },
	reg_period             =	{addr = 0x2E, len = 3, val_bits = 20, val = 0  },
	-- reg_cf_a_cnt           =	{addr = 0x2F, len = 3, val_bits = 24, val = 0  },
	-- reg_cf_b_cnt           =	{addr = 0x30, len = 3, val_bits = 24, val = 0  },
	-- reg_cf_c_cnt           =	{addr = 0x31, len = 3, val_bits = 24, val = 0  },
	-- reg_cf_cnt             =	{addr = 0x32, len = 3, val_bits = 24, val = 0  },
	reg_cfp_a_cnt          =	{addr = 0x33, len = 3, val_bits = 24, val = 0  },
	reg_cfp_b_cnt          =	{addr = 0x34, len = 3, val_bits = 24, val = 0  },
	reg_cfp_c_cnt          =	{addr = 0x35, len = 3, val_bits = 24, val = 0  },
	reg_cfp_cnt            =	{addr = 0x36, len = 3, val_bits = 24, val = 0  },
	-- reg_cfn_a_cnt          =	{addr = 0x37, len = 3, val_bits = 24, val = 0  },
	-- reg_cfn_b_cnt          =	{addr = 0x38, len = 3, val_bits = 24, val = 0  },
	-- reg_cfn_c_cnt          =	{addr = 0x39, len = 3, val_bits = 24, val = 0  },
	-- reg_cfn_cnt            =	{addr = 0x3A, len = 3, val_bits = 24, val = 0  },
	-- reg_cfq_a_cnt          =	{addr = 0x3B, len = 3, val_bits = 24, val = 0  },
	-- reg_cfq_b_cnt          =	{addr = 0x3C, len = 3, val_bits = 24, val = 0  },
	-- reg_cfq_c_cnt          =	{addr = 0x3D, len = 3, val_bits = 24, val = 0  },
	-- reg_cfq_cnt            =	{addr = 0x3E, len = 3, val_bits = 24, val = 0  },
	-- reg_cfq1_cnt           =	{addr = 0x3F, len = 3, val_bits = 24, val = 0  },
	-- reg_cfq2_cnt           =	{addr = 0x40, len = 3, val_bits = 24, val = 0  },
	-- reg_cfq3_cnt           =	{addr = 0x41, len = 3, val_bits = 24, val = 0  },
	-- reg_cfq4_cnt           =	{addr = 0x42, len = 3, val_bits = 24, val = 0  },
	-- reg_cfs_a_cnt          =	{addr = 0x43, len = 3, val_bits = 24, val = 0  },
	-- reg_cfs_b_cnt          =	{addr = 0x44, len = 3, val_bits = 24, val = 0  },
	-- reg_cfs_c_cnt          =	{addr = 0x45, len = 3, val_bits = 24, val = 0  },
	-- reg_cfs_cnt            =	{addr = 0x46, len = 3, val_bits = 24, val = 0  },
	reg_pf_a               =	{addr = 0x47, len = 3, val_bits = 24, val = 0  },
	reg_pf_b               =	{addr = 0x48, len = 3, val_bits = 24, val = 0  },
	reg_pf_c               =	{addr = 0x49, len = 3, val_bits = 24, val = 0  },
	reg_pf                 =	{addr = 0x4A, len = 3, val_bits = 24, val = 0  },
	-- reg_line_watthr        =	{addr = 0x4B, len = 3, val_bits = 24, val = 0  },
	-- reg_line_varhr         =	{addr = 0x4C, len = 3, val_bits = 24, val = 0  },
	-- reg_sign               =	{addr = 0x4D, len = 3, val_bits = 24, val = 0  },
	-- reg_angle_ab           =	{addr = 0x4E, len = 3, val_bits = 16, val = 0  },
	-- reg_angle_bc           =	{addr = 0x4F, len = 3, val_bits = 16, val = 0  },
	-- reg_angle_ac           =	{addr = 0x50, len = 3, val_bits = 16, val = 0  },
	-- reg_angle_a            =	{addr = 0x51, len = 3, val_bits = 16, val = 0  },
	-- reg_angle_b            =	{addr = 0x52, len = 3, val_bits = 16, val = 0  },
	-- reg_angle_c            =	{addr = 0x53, len = 3, val_bits = 16, val = 0  },
	reg_status1            =	{addr = 0x54, len = 3, val_bits = 24, val = 0  },
	-- reg_status2            =	{addr = 0x55, len = 3, val_bits = 24, val = 0  },
	-- reg_i_sum              =	{addr = 0x57, len = 3, val_bits = 24, val = 0  },
	-- reg_i_sum_rms          =	{addr = 0x58, len = 3, val_bits = 24, val = 0  },
	-- reg_i_sum_fast_rms     =	{addr = 0x59, len = 3, val_bits = 24, val = 0  },
	-- reg_var_a              =	{addr = 0x5A, len = 3, val_bits = 24, val = 0  },
	-- reg_var_b              =	{addr = 0x5B, len = 3, val_bits = 24, val = 0  },
	-- reg_var_c              =	{addr = 0x5C, len = 3, val_bits = 24, val = 0  },
	-- reg_var                =	{addr = 0x5D, len = 3, val_bits = 24, val = 0  },
	reg_reserved1          =	{addr = 0x5E, len = 3, val_bits = 10, val = 0  },
	reg_gain1              =	{addr = 0x60, len = 3, val_bits = 24, val = 0  },
	reg_gain2              =	{addr = 0x61, len = 3, val_bits = 20, val = 0  },
	reg_irms_p1            =	{addr = 0x62, len = 3, val_bits = 24, val = 0  },
	reg_irms_p2            =	{addr = 0x63, len = 3, val_bits = 24, val = 0  },
	reg_ia_phcal           =	{addr = 0x64, len = 3, val_bits = 24, val = 0  },
	reg_ib_phcal           =	{addr = 0x65, len = 3, val_bits = 24, val = 0  },
	reg_ic_phcal           =	{addr = 0x66, len = 3, val_bits = 24, val = 0  },
	reg_va_phcal           =	{addr = 0x67, len = 3, val_bits = 24, val = 0  },
	reg_vb_phcal           =	{addr = 0x68, len = 3, val_bits = 24, val = 0  },
	reg_vc_phcal           =	{addr = 0x69, len = 3, val_bits = 24, val = 0  },
	-- reg_var_phcal_i        =	{addr = 0x6A, len = 3, val_bits = 15, val = 0  },
	-- reg_var_phcal_v        =	{addr = 0x6B, len = 3, val_bits = 15, val = 0  },
	-- reg_ic_rmsgn           =	{addr = 0x6D, len = 3, val_bits = 16, val = 0  },
	-- reg_ib_rmsgn           =	{addr = 0x6E, len = 3, val_bits = 16, val = 0  },
	-- reg_ia_rmsgn           =	{addr = 0x6F, len = 3, val_bits = 16, val = 0  },
	-- reg_in_rmsgn           =	{addr = 0x70, len = 3, val_bits = 16, val = 0  },
	-- reg_va_rmsgn           =	{addr = 0x73, len = 3, val_bits = 16, val = 0  },
	-- reg_vb_rmsgn           =	{addr = 0x74, len = 3, val_bits = 16, val = 0  },
	-- reg_vc_rmsgn           =	{addr = 0x75, len = 3, val_bits = 16, val = 0  },
	-- reg_ic_rmsos           =	{addr = 0x78, len = 3, val_bits = 24, val = 0  },
	-- reg_ib_rmsos           =	{addr = 0x79, len = 3, val_bits = 24, val = 0  },
	-- reg_ia_rmsos           =	{addr = 0x7A, len = 3, val_bits = 24, val = 0  },
	-- reg_in_rmsos           =	{addr = 0x7B, len = 3, val_bits = 24, val = 0  },
	-- reg_va_rmsos           =	{addr = 0x7E, len = 3, val_bits = 24, val = 0  },
	-- reg_vb_rmsos           =	{addr = 0x7F, len = 3, val_bits = 24, val = 0  },
	-- reg_vc_rmsos           =	{addr = 0x80, len = 3, val_bits = 24, val = 0  },
	-- reg_wa_var_los_a       =	{addr = 0x82, len = 3, val_bits = 24, val = 0  },
	-- reg_wa_var_los_b       =	{addr = 0x83, len = 3, val_bits = 24, val = 0  },
	-- reg_wa_var_los_c       =	{addr = 0x84, len = 3, val_bits = 24, val = 0  },
	-- reg_fvar_los_a         =	{addr = 0x85, len = 3, val_bits = 24, val = 0  },
	-- reg_fvar_los_b         =	{addr = 0x86, len = 3, val_bits = 24, val = 0  },
	-- reg_fvar_los_c         =	{addr = 0x87, len = 3, val_bits = 24, val = 0  },
	reg_var_wa_creep       =	{addr = 0x88, len = 3, val_bits = 24, val = 0  },
	reg_var_wa_creep2      =	{addr = 0x89, len = 3, val_bits = 24, val = 0  },
	-- reg_revp_rms_creep     =	{addr = 0x8A, len = 3, val_bits = 24, val = 0  },
	-- reg_fast_rms_ctrl      =	{addr = 0x8B, len = 3, val_bits = 24, val = 0  },
	-- reg_i_v_pklvl          =	{addr = 0x8C, len = 3, val_bits = 24, val = 0  },
	-- reg_isumlvl            =	{addr = 0x8D, len = 3, val_bits = 24, val = 0  },
	-- reg_sagcyc_zxtout      =	{addr = 0x8E, len = 3, val_bits = 24, val = 0  },
	-- reg_saglvl_linecyc     =	{addr = 0x8F, len = 3, val_bits = 24, val = 0  },
	-- reg_in_phcal           =	{addr = 0x90, len = 3, val_bits = 24, val = 0  },
	-- reg_isum_rmsgn         =	{addr = 0x91, len = 3, val_bits = 16, val = 0  },
	-- reg_isum_rmsos         =	{addr = 0x92, len = 3, val_bits = 24, val = 0  },
	reg_adc_pd             =	{addr = 0x93, len = 3, val_bits = 11, val = 0  },
	reg_reserved2          =	{addr = 0x94, len = 3, val_bits = 16, val = 0  },
	reg_mode1              =	{addr = 0x96, len = 3, val_bits = 24, val = 0  },
	reg_mode2              =	{addr = 0x97, len = 3, val_bits = 24, val = 0  },
	reg_mode3              =	{addr = 0x98, len = 3, val_bits = 24, val = 0  },
	reg_mask1              =	{addr = 0x9A, len = 3, val_bits = 24, val = 0  },
	reg_mask2              =	{addr = 0x9B, len = 3, val_bits = 24, val = 0  },
	reg_rst_eng            =	{addr = 0x9D, len = 3, val_bits = 24, val = 0  },
	reg_usr_wrprot         =	{addr = 0x9E, len = 3, val_bits = 16, val = 0  },
	reg_soft_reset         =	{addr = 0x9F, len = 3, val_bits = 24, val = 0  },
	reg_ic_chgn            =	{addr = 0xA1, len = 3, val_bits = 16, val = 0  },
	reg_ib_chgn            =	{addr = 0xA2, len = 3, val_bits = 16, val = 0  },
	reg_ia_chgn            =	{addr = 0xA3, len = 3, val_bits = 16, val = 0  },
	-- reg_in_chgn            =	{addr = 0xA4, len = 3, val_bits = 16, val = 0  },
	-- reg_va_chgn            =	{addr = 0xA7, len = 3, val_bits = 16, val = 0  },
	-- reg_vb_chgn            =	{addr = 0xA8, len = 3, val_bits = 16, val = 0  },
	-- reg_vc_chgn            =	{addr = 0xA9, len = 3, val_bits = 16, val = 0  },
	-- reg_ic_chos            =	{addr = 0xAC, len = 3, val_bits = 16, val = 0  },
	-- reg_ib_chos            =	{addr = 0xAD, len = 3, val_bits = 16, val = 0  },
	-- reg_ia_chos            =	{addr = 0xAE, len = 3, val_bits = 16, val = 0  },
	-- reg_in_chos            =	{addr = 0xAF, len = 3, val_bits = 16, val = 0  },
	-- reg_va_chos            =	{addr = 0xB2, len = 3, val_bits = 16, val = 0  },
	-- reg_vb_chos            =	{addr = 0xB3, len = 3, val_bits = 16, val = 0  },
	-- reg_vc_chos            =	{addr = 0xB4, len = 3, val_bits = 16, val = 0  },
	-- reg_wattgn_a           =	{addr = 0xB6, len = 3, val_bits = 16, val = 0  },
	-- reg_wattgn_b           =	{addr = 0xB7, len = 3, val_bits = 16, val = 0  },
	-- reg_wattgn_c           =	{addr = 0xB8, len = 3, val_bits = 16, val = 0  },
	-- reg_vargn_a            =	{addr = 0xB9, len = 3, val_bits = 16, val = 0  },
	-- reg_vargn_b            =	{addr = 0xBA, len = 3, val_bits = 16, val = 0  },
	-- reg_vargn_c            =	{addr = 0xBB, len = 3, val_bits = 16, val = 0  },
	-- reg_fvargn_a           =	{addr = 0xBC, len = 3, val_bits = 16, val = 0  },
	-- reg_fvargn_b           =	{addr = 0xBD, len = 3, val_bits = 16, val = 0  },
	-- reg_fvargn_c           =	{addr = 0xBE, len = 3, val_bits = 16, val = 0  },
	-- reg_vagn_a             =	{addr = 0xBF, len = 3, val_bits = 16, val = 0  },
	-- reg_vagn_b             =	{addr = 0xC0, len = 3, val_bits = 16, val = 0  },
	-- reg_vagn_c             =	{addr = 0xC1, len = 3, val_bits = 16, val = 0  },
	reg_wattos_a           =	{addr = 0xC2, len = 3, val_bits = 16, val = 0  },
	reg_wattos_b           =	{addr = 0xC3, len = 3, val_bits = 16, val = 0  },
	reg_wattos_c           =	{addr = 0xC4, len = 3, val_bits = 16, val = 0  },
	-- reg_varos_a            =	{addr = 0xC5, len = 3, val_bits = 16, val = 0  },
	-- reg_varos_b            =	{addr = 0xC6, len = 3, val_bits = 16, val = 0  },
	-- reg_varos_c            =	{addr = 0xC7, len = 3, val_bits = 16, val = 0  },
	-- reg_fvaros_a           =	{addr = 0xC8, len = 3, val_bits = 16, val = 0  },
	-- reg_fvaros_b           =	{addr = 0xC9, len = 3, val_bits = 16, val = 0  },
	-- reg_fvaros_c           =	{addr = 0xCA, len = 3, val_bits = 16, val = 0  },
	-- reg_vaos_a             =	{addr = 0xCB, len = 3, val_bits = 16, val = 0  },
	-- reg_vaos_b             =	{addr = 0xCC, len = 3, val_bits = 16, val = 0  },
	-- reg_vaos_c             =	{addr = 0xCD, len = 3, val_bits = 16, val = 0  },
	reg_cfdiv              =	{addr = 0xCE, len = 3, val_bits = 12, val = 0  },
	reg_at_sel             =	{addr = 0xCF, len = 3, val_bits = 9 , val = 0  },
	reg_reserved3          =	{addr = 0xD0, len = 3, val_bits = 16, val = 0  },
    reg_eng_wrprot         =	{addr = 0xDD, len = 3, val_bits = 16, val = 0  },
    reg_eng_otpcode        =	{addr = 0xE1, len = 3, val_bits = 16, val = 0  },
}

--[[
@description: 
@return {*}
--]]
local function showhex(buf)
    for i = 1, #buf do
        print(string.format("%02X ", buf[i]))
    end
end

--串口接收字符串转化为表
local function str_to_table(str)
    local rx_table = {}
    for i = 1, #str do
        local _, p = pack.unpack(str, 'b', i)
        rx_table[i] = p
    end
    -- print(table.concat(rx_table, " "))
    return rx_table
end

--[[
@description: 
@return {*}
--]]
local function bl6552_rx_date_check(buf)
    local crc = 0x00
    
    for i=1, #buf-1 do
        crc = crc + buf[i]
    end
    if buf[2] == 0xAA then
        crc = crc - 0xAA
    end

    -- 先取反可以最后保留8位
    crc = ~crc & 0xFF
    if crc == buf[#buf] then 
        return true
    end
    log.info("bl6552_rx_date_check", "crc err")
    -- showhex(buf)
    print(string.format("%02X,%02X ", crc, buf[#buf]))
    return false
end

--[[
@description: bl6552的发送计算，和0942不同，参与计算的不包含芯片地址
@return {*}
--]]
local function bl6552_tx_date_cal(buf,len)
    local crc = 0x00
    for i=2, len do
        crc = crc + buf[i]
    end
    return ~crc & 0xFF
end


-- 发送函数
local function write(uid, str)
    local i
    bl_writeBuff = {}
    for i = 1, #str, buff_len do
        table.insert(bl_writeBuff, str:sub(i, i + buff_len - 1))
    end
    local wrf = {}
    for i = 1, #table.concat(bl_writeBuff) do
        local _, p = pack.unpack(table.concat(bl_writeBuff), 'b', i)
        wrf[i] = p
    end
    -- print(table.concat(wrf, " "))

    if not bl_writeBusy then
        bl_writeBusy = true
        uart.write(uid, table.remove(bl_writeBuff, 1))
    end
end


--发送数据,数组为入参
local function bl6552_uart_tx_data(buf, len)
    local send_str = ''
    for i=1,len do 
        send_str = send_str..pack.pack('b', buf[i])
    end
    -- if debug == 1 then
    --     log.info("bl6552_uart_tx_data", "send_str", #send_str)
    --     showhex(buf)
    -- end
    
    write(UART_BL6552, send_str)
end


-- 计算起始字节 RW为0 是读 1 是写 
local function bl6552_cal_first_byte(RW)

    --简单的
    if 0 == RW then
        return BL6552_ADDR_READ
    else
        return BL6552_ADDR_WRITE
    end

end

local function bl6552_read_reg_with_addr(addr,len,val_bits)
    local buf = {}
    buf[1] = bl6552_cal_first_byte(0)
    buf[2] = addr
    bl6552_uart_tx_data(buf,2)
     --等待回应
     local ret,rx_buf = sys.waitUntil(BL_UART_RX_DONE, 500)
     if ret == false then
         log.info("bl6552_read_read_reg", "timeout")
         return false
     end
     -- BL0932 的返回字节是固定len+1个 一般3个数据+1个校验字节
     if #rx_buf ~= len+1 then
         log.info("bl6552_read_read_reg", "rx_buf len err")
         return false
     end
    --  if debug ==1 then
    --      log.info("bl6552_rx_date_proc", "rx_buf",rx_buf)
    --  end
     local rx_hex = str_to_table(rx_buf)
    -- showhex(rx_hex)
    
    local all_hex = {}
    for i = 2, #buf do  -- 6552计算校验 是寄存器地址和数据，不包含芯片地址，这和0942不同
        table.insert(all_hex, buf[i])
    end
    for i = 1, #rx_hex do
        table.insert(all_hex, rx_hex[i])
    end
    
    local crc_result = bl6552_rx_date_check(all_hex)
    if true ~= crc_result then
        log.info("bl6552_read_read_reg", "crc err")
        return false
    end
    if len > 3 then
        return rx_hex
    else 
        -- 3个字节及以下是寄存器
        local val = 0
        for i=1,len do
            
            local temp = rx_hex[i] << (8*(i-1))
            val = val | temp
            -- log.info("bl6552_read_read_reg", "rx_hex", val,temp, i, rx_hex[i])
        end
        if val_bits~=0 then
            -- lua5.4 位移运算优先级低于加减乘除
            local mask = (1 << val_bits) - 1
            val = val & mask
        end
        return val
    end
end

local function bl6552_read_reg(reg)
    local ret = -1
    local val = bl6552_read_reg_with_addr(reg.addr, reg.len, reg.val_bits)
    if type(val) == "number" then
        reg.val = val
        -- if debug == 1 then
        --     log.info("bl6552_read_reg:", string.format( "0X%02X",reg.addr ),  string.format( "0X%02X",val ) ,string.format( "%d", val ))
        -- end
        ret = BL6552_SUCCESS
    else
        -- reg.val = 0
        log.info("bl6552_read_reg:", (string.format( "0X%02X",reg.addr )), "read err")
        ret = BL6552_FAIL
    end
    return ret
end

local  function bl6552_read_reg_all()
    for k,v in pairs(bl6552_reg_all) do
        local val = bl6552_read_reg_with_addr(v.addr,v.len,v.val_bits)
        if type(val) == "number" then
            v.val = val
            -- if debug == 1 then
            --     -- log.info("bl6552_read_reg_all:".. k , val)
            -- end
        else
            log.info("bl6552_read_reg_all:".. k , "read err")
        end
        sys.wait(100)
    end
    
    -- for k,v in pairs(bl6552_reg_all) do
    --     log.info("bl6552_test", k, string.format( "  0X%02X   0X%02X  %d ",v.addr,v.val,v.val ))
    -- end
end



local function point_reserved(num,point_num)
    local a = math.floor( num*(10^point_num) )
    local ret = a / (10^point_num)
    return  ret
end

local function bl6552_cal_i(val)
    local ret = point_reserved(val / Ki,2)
    return ret
end

local function bl6552_cal_v(val)
    local ret = point_reserved(val / Ku,2)
    return ret
    -- return val / Ku
end

local function bl6552_cal_p_x(val,val_bits)
    local mask = 1 << (val_bits-1)
    if 0 == (mask&val) then
        local ret = point_reserved(val / Kp,2)
        return ret
        -- if ret < -0.00001 then
        --     return -ret
        -- else
        --     return ret
        -- end
    else
        local val_ret = val - (1 << val_bits)
        local val1 = val_ret / Kp
        return val1
        -- if val1 < -0.00001 then
        --     return -val1
        -- else
        --     return val1
        -- end
    end
end

local function bl6552_cal_p_sum(val,val_bits)
    local mask = 1 << (val_bits-1)
    if 0 == (mask&val) then
        local ret = point_reserved(val / Kp_sum,2)
        return ret
        -- log.info("bl6552_cal_p", "ret", ret)
        -- if ret < -0.00001 then
        --     return -ret
        -- else
        --     return ret
        -- end
    else
        local val_ret = val - (1 << val_bits)
        local val1 = val_ret / Kp_sum
        return val1
        -- log.info("bl6552_cal_p", "val1", val1)
        -- if val1 < -0.00001 then
        --     return -val1
        -- else
        --     return val1
        -- end
    end
end


local function bl6552_cal_ep_x(val)
    return val * Kcf
end

local function bl6552_cal_ep_sum(val)
    return val * Kcf_sum
end

local function bl6552_cal_f(val)
    return 10000000/val
end

local function bl6552_cal_pf(val)
    local temp = 0
    if val >= (0x800000-1) then
        
        temp = 0x1000000 - val
    else
        temp = val
    end
    return temp/0x800000
end



local function bl6552_rx_date_proc(buf)
    --暂时空着

    -- log.info("bl6552_rx_date_proc", "send_str", #buf)
    -- showhex(buf)
end


local function bl6552_uart_init()
    uart.setup(UART_BL6552, 
                bl6552_uart_pram.baudrate, 
                bl6552_uart_pram.dataBits, 
                bl6552_uart_pram.stopBits, 
                bl6552_uart_pram.parity
            )
    sys.subscribe(BL_UART_RX_DONE, function(uid, data)
        if uid == UART_BL6552 then
            bl6552_rx_date_proc(data)
        end
    end)
    -- log.info("bl6552_uart_init","bl uart init")
    uart.on(UART_BL6552, "receive", function(uid)
        -- log.info("bl6552 on receive")
        table.insert(bl_data_queue, uart.read(uid, buff_len))
        local rdata = table.concat(bl_data_queue)
        sys.publish(BL_UART_RX_DONE, rdata)
        bl_data_queue = {}
    end)

    uart.on(UART_BL6552, "sent", function()
        if #bl_writeBuff == 0 then
            bl_writeBusy = false

        else
            bl_writeBusy = true
            uart.write(UART_BL6552, table.remove(bl_writeBuff, 1))
            -- log.info("bl_gmir", "uart bl send ing...")
        end
    end)
end


local function bl6552_write_reg_with_addr(addr,val,len)
    local buf = {}
    local temp = 0xFFFFFF &  math.floor(val)
    buf[1] = bl6552_cal_first_byte(1)
    buf[2] = addr
    for i=1,len do
        buf[i+2] = (temp>>(8*(i-1))) & 0xFF
    end
    local buf_len = len + 2
    buf[buf_len+1] = bl6552_tx_date_cal(buf,buf_len)
    buf_len = buf_len + 1
    bl6552_uart_tx_data(buf,buf_len)
    sys.wait(5)
end

local function bl6552_write_reg(reg,val)
    -- 写的时候都是3字节
    -- log.info("bl6552_write_reg ", string.format(" 0x%02X 0x%02X %d ",reg.addr, val, val) )
    bl6552_write_reg_with_addr(reg.addr, val,3)
    -- bl6552_write_reg_with_addr(val,reg.addr,reg.len)
end

-- 写使能，重试1次
-- 保护
--[[
@description: 写使能，保护的范围为0x60-0x9D和0xA0-0xD0
--]]
local function bl6552_write_enable()
    local unlock1 = 0x5555
    local unlock2 = 0x0950
    local unlock3 = 0x0D82
    bl6552_write_reg(bl6552_reg_all.reg_usr_wrprot,unlock1)

    bl6552_write_reg(bl6552_reg_all.reg_eng_otpcode,unlock2)

    bl6552_write_reg(bl6552_reg_all.reg_eng_wrprot,unlock3)

    -- bl6552_read_reg(bl6552_reg_all.reg_usr_wrprot)
    -- bl6552_read_reg(bl6552_reg_all.reg_eng_otpcode)
    -- bl6552_read_reg(bl6552_reg_all.reg_eng_wrprot)
    -- if (0x5555 == bl6552_reg_all.reg_usr_wrprot.val)
    -- -- and (0x00 ~= bl6552_reg_all.reg_eng_otpcode.val)
    -- -- and (0x00 ~= bl6552_reg_all.reg_eng_wrprot.val)
    -- then
    --     -- log.info("bl6552_write_enable","write enable success")
    --     return BL6552_SUCCESS
    -- else
    --     bl6552_write_reg(bl6552_reg_all.reg_usr_wrprot,unlock1)
    --     sys.wait(5)
    --     bl6552_write_reg(bl6552_reg_all.reg_eng_otpcode,unlock2)
    --     sys.wait(5)
    --     bl6552_write_reg(bl6552_reg_all.reg_eng_wrprot,unlock3)
    --     sys.wait(5)

    --     bl6552_read_reg(bl6552_reg_all.reg_usr_wrprot)
    --     bl6552_read_reg(bl6552_reg_all.reg_eng_otpcode)
    --     bl6552_read_reg(bl6552_reg_all.reg_eng_wrprot)
    --     if (0x0000 ~= bl6552_reg_all.reg_usr_wrprot.val)
    --         -- and (0x0000 ~= bl6552_reg_all.reg_eng_otpcode.val)
    --         -- and (0x0000 ~= bl6552_reg_all.reg_eng_wrprot.val)
    --     then
    --         -- log.info("bl6552_write_enable","write enable success")
    --         return BL6552_SUCCESS
    --     end
    -- end
    -- -- log.info("bl6552_write_enable","write enable fail")
    -- return BL6552_FAIL
end
--[[
@description: 写禁止，保护的范围为0x60-0x9D和0xA0-0xD0
--]]
local function bl6552_write_disable()
    local lock = 0x0000
    bl6552_write_reg(bl6552_reg_all.reg_eng_wrprot,0x0000)

    bl6552_write_reg(bl6552_reg_all.reg_eng_otpcode,0x0000)

    bl6552_write_reg(bl6552_reg_all.reg_usr_wrprot,0x0000)
    
    -- bl6552_read_reg(bl6552_reg_all.reg_usr_wrprot)
    -- bl6552_read_reg(bl6552_reg_all.reg_eng_otpcode)
    -- bl6552_read_reg(bl6552_reg_all.reg_eng_wrprot)
    -- if 0x0000 == (bl6552_reg_all.reg_usr_wrprot.val) 
    -- -- and 0x0000 == (bl6552_reg_all.reg_eng_otpcode.val)
    -- then
    --     -- log.info("bl6552_write_disable","write disable success")
    --     return BL6552_SUCCESS
    -- else
    --     bl6552_write_reg(bl6552_reg_all.reg_usr_wrprot,0x0000)
    --     sys.wait(5)
    --     bl6552_write_reg(bl6552_reg_all.reg_eng_otpcode,0x0000)
    --     sys.wait(5)
    --     bl6552_write_reg(bl6552_reg_all.reg_eng_wrprot,0x0000)
    --     sys.wait(5)
       
    --     bl6552_read_reg(bl6552_reg_all.reg_usr_wrprot)
    --     bl6552_read_reg(bl6552_reg_all.reg_eng_otpcode)
    --     bl6552_read_reg(bl6552_reg_all.reg_eng_wrprot)
    --     if 0x0000 == (bl6552_reg_all.reg_usr_wrprot.val )
    --         -- and 0x0000 == (bl6552_reg_all.reg_eng_otpcode.val)
    --     then
    --         -- log.info("bl6552_write_disable","write disable success")
    --         return BL6552_SUCCESS
    --     end
    -- end
    -- -- log.info("bl6552_write_disable","write disable fail")
    -- return BL6552_FAIL
end

-- local function bl6552_clear_e_params()
--     -- bl6552_write_enable()
--     bl6552_write_reg(bl6552_reg_all.reg_soft_reset,0x5A5A5A)
--     -- bl6552_write_disable()
-- end


local function bl6552_reg_reset()
    -- bl6552_write_enable()
    bl6552_write_reg(bl6552_reg_all.reg_soft_reset,0x55AA55)
    -- bl6552_write_disable()
end

--[[
@description: 装载芯片工作的模拟电路参数
@return {*}
--]]
local function bl6552_load_param()
    local data = 0x000003
    
    bl6552_write_reg_with_addr(0xE7,data,3)
    -- sys.wait(5)
    data = 0x000024
    bl6552_write_reg_with_addr(0xE3,data,3)
    -- sys.wait(5)
    bl6552_write_enable()
    data = 0x0037C0
    bl6552_write_reg_with_addr(0xD1,data,3)
    -- sys.wait(5)
    data = 0x000000
    bl6552_write_reg_with_addr(0xD2,data,3)
    -- sys.wait(5)
    data = 0x001010
    bl6552_write_reg_with_addr(0xD3,data,3)
    -- sys.wait(5)
    data = 0x000024
    bl6552_write_reg_with_addr(0xD4,data,3)
    -- sys.wait(5)
    data = 0x007B40
    bl6552_write_reg_with_addr(0xD5,data,3)
    -- sys.wait(5)
    data = 0x00000C
    bl6552_write_reg_with_addr(0xD6,data,3)
    -- sys.wait(5)
    data = 0x006B1F
    bl6552_write_reg_with_addr(0xD7,data,3)
    -- sys.wait(5)
    data = 0x000012
    bl6552_write_reg_with_addr(0xD8,data,3)
    -- sys.wait(5)
    data = 0x000030
    bl6552_write_reg_with_addr(0xD9,data,3)
    -- sys.wait(5)
    data = 0x000005
    bl6552_write_reg_with_addr(0xDA,data,3)
    -- sys.wait(5)
    
    return BL6552_SUCCESS
end



local function bl6552_calset_proc()
    -- bl6552_write_enable()
    --防潜动   功率1w
    bl6552_write_enable()
    bl6552_write_reg(bl6552_reg_all.reg_var_wa_creep,p_var_wa_creep)
    -- sys.wait(5)
    bl6552_read_reg(bl6552_reg_all.reg_var_wa_creep)
    log.info("bl6552_calset_proc", "reg_var_wa_creep", bl6552_reg_all.reg_var_wa_creep.val)

    --增益 Ib 1.0修正参数
    local data = 0xFDAC
    bl6552_write_reg(bl6552_reg_all.reg_ia_chgn,data)
    -- sys.wait(5)
    data = 0xFE1D
    bl6552_write_reg(bl6552_reg_all.reg_ib_chgn,data)
    -- sys.wait(5)
    data = 0xFEE5
    bl6552_write_reg(bl6552_reg_all.reg_ic_chgn,data)
    -- sys.wait(5)

    --相位 Ib 0.5L修正参数
    --相位分段补偿修正，根据互感器确定分段位置
    -- data = 0x020000
    -- bl6552_write_reg(bl6552_reg_all.reg_irms_p1,data)
    -- data = 0x200000
    -- bl6552_write_reg(bl6552_reg_all.reg_irms_p2,data)

    data = 0x191919
    bl6552_write_reg(bl6552_reg_all.reg_ia_phcal,data)
    -- sys.wait(5)
    data = 0x191919
    bl6552_write_reg(bl6552_reg_all.reg_ib_phcal,data)
    -- sys.wait(5)
    data = 0x191919
    bl6552_write_reg(bl6552_reg_all.reg_ic_phcal,data)
    -- sys.wait(5)
    data = 0x00
    bl6552_write_reg(bl6552_reg_all.reg_va_phcal,data)
    -- sys.wait(5)
    data = 0x00
    bl6552_write_reg(bl6552_reg_all.reg_vb_phcal,data)
    -- sys.wait(5)
    data = 0x00
    bl6552_write_reg(bl6552_reg_all.reg_vc_phcal,data)
    -- sys.wait(5)


    -- 有功功率小信号修正 0.05Ib 1.0
    data = 0x00
    bl6552_write_reg(bl6552_reg_all.reg_wattos_a,data)
    -- sys.wait(5)
    data = 0x00
    bl6552_write_reg(bl6552_reg_all.reg_wattos_b,data)
    -- sys.wait(5)
    data = 0x00
    bl6552_write_reg(bl6552_reg_all.reg_wattos_c,data)
    -- sys.wait(5)


    -- bl6552_write_disable()
end



--[[
@description: 
@return {*}
--]]

local function bl6552_config()
    bl6552_write_enable()

    bl6552_reg_reset()
    -- sys.wait(5)

    bl6552_load_param()

    
    bl6552_write_reg(bl6552_reg_all.reg_adc_pd,0xC3)
    -- sys.wait(5)
    -- bl6552_read_reg(bl6552_reg_all.reg_adc_pd)

    --16分频
    bl6552_write_reg(bl6552_reg_all.reg_cfdiv,0x10)
    -- sys.wait(5)
    bl6552_write_reg(bl6552_reg_all.reg_mode3,0x011200)
    -- sys.wait(5)
    -- sys.wait(1)
    -- -- bl6552_read_reg(bl6552_reg_all.reg_cfdiv)
    -- -- bl6552_read_reg(bl6552_reg_all.reg_mode3)

    bl6552_calset_proc()
    -- sys.wait(2)

    -- bl6552_write_enable()
    -- 快速有效值经过高通
    bl6552_write_reg(bl6552_reg_all.reg_mode1,0x400000)
    -- sys.wait(5)

    -- 电能脉冲读后清零设置,Reg46~2F设置为读后清零
    bl6552_write_reg(bl6552_reg_all.reg_rst_eng,0xFFFFFF)
    -- sys.wait(5)
    -- sys.wait(2)
    -- bl6552_read_reg(bl6552_reg_all.reg_mode1)
    -- bl6552_read_reg(bl6552_reg_all.reg_rst_eng)

    -- log.info("bl6552_config","reg_mode1",bl6552_reg_all.reg_mode1.val)
    -- log.info("bl6552_config","reg_rst_eng",bl6552_reg_all.reg_rst_eng.val)
    -- 电参数运算系统复位，Reg3B~2F清零   这里不清了，减少重启带来的损失
    -- bl6552_clear_e_params()


    bl6552_write_disable()

    bl6552_read_reg_all()
    
end


local function bl6552_read_Eregs()

    --使用防潜动设定值判断芯片有没有异常
    bl6552_read_reg(bl6552_reg_all.reg_var_wa_creep)
    if(p_var_wa_creep ~= bl6552_reg_all.reg_var_wa_creep.val) then
        log.info("bl6552_read_Eregs","reg_var_wa_creep error, reset chip",p_var_wa_creep,bl6552_reg_all.reg_var_wa_creep.val)
        bl6552_config()
        -- bl6552_calset_proc()
    else
        --电流有效值转换
        bl6552_read_reg(bl6552_reg_all.reg_ia_rms)
        bl6552_read_reg(bl6552_reg_all.reg_ib_rms)
        bl6552_read_reg(bl6552_reg_all.reg_ic_rms)

        bl6552_data.ia = bl6552_cal_i(bl6552_reg_all.reg_ia_rms.val)
        bl6552_data.ib = bl6552_cal_i(bl6552_reg_all.reg_ib_rms.val)
        bl6552_data.ic = bl6552_cal_i(bl6552_reg_all.reg_ic_rms.val)
        

        --电压有效值转换
        bl6552_read_reg(bl6552_reg_all.reg_va_rms)
        bl6552_read_reg(bl6552_reg_all.reg_vb_rms)
        bl6552_read_reg(bl6552_reg_all.reg_vc_rms)

        bl6552_data.ua = bl6552_cal_v(bl6552_reg_all.reg_va_rms.val)
        bl6552_data.ub = bl6552_cal_v(bl6552_reg_all.reg_vb_rms.val)
        bl6552_data.uc = bl6552_cal_v(bl6552_reg_all.reg_vc_rms.val)

        --有功功率转换
        bl6552_read_reg(bl6552_reg_all.reg_watt_a)
        bl6552_read_reg(bl6552_reg_all.reg_watt_b)
        bl6552_read_reg(bl6552_reg_all.reg_watt_c)
        bl6552_read_reg(bl6552_reg_all.reg_watt)

        bl6552_data.pa = bl6552_cal_p_x(bl6552_reg_all.reg_watt_a.val,bl6552_reg_all.reg_watt_a.val_bits) * bl6552_data.factor_pa
        bl6552_data.pb = bl6552_cal_p_x(bl6552_reg_all.reg_watt_b.val,bl6552_reg_all.reg_watt_b.val_bits) * bl6552_data.factor_pb
        bl6552_data.pc = bl6552_cal_p_x(bl6552_reg_all.reg_watt_c.val,bl6552_reg_all.reg_watt_c.val_bits) * bl6552_data.factor_pc
        bl6552_data.p = bl6552_cal_p_sum(bl6552_reg_all.reg_watt.val,bl6552_reg_all.reg_watt.val_bits)* bl6552_data.factor_p_sum



        --线电压频率转换，由于只有一个寄存器，需要设置通道，才能获得对应的某一相的频率，缺省为A
        bl6552_read_reg(bl6552_reg_all.reg_period)
        bl6552_data.freq = bl6552_cal_f(bl6552_reg_all.reg_period.val)



        --正有功电能转换处理
        bl6552_read_reg(bl6552_reg_all.reg_cfp_a_cnt)
        bl6552_read_reg(bl6552_reg_all.reg_cfp_b_cnt)
        bl6552_read_reg(bl6552_reg_all.reg_cfp_c_cnt)
        bl6552_read_reg(bl6552_reg_all.reg_cfp_cnt)

        bl6552_data.ep_a = bl6552_cal_ep_x(bl6552_reg_all.reg_cfp_a_cnt.val)	* bl6552_data.factor_pa 	+ bl6552_data.ep_init_a
        bl6552_data.ep_b = bl6552_cal_ep_x(bl6552_reg_all.reg_cfp_b_cnt.val)	* bl6552_data.factor_pb 	+ bl6552_data.ep_init_b
        bl6552_data.ep_c = bl6552_cal_ep_x(bl6552_reg_all.reg_cfp_c_cnt.val)	* bl6552_data.factor_pc 	+ bl6552_data.ep_init_c
        bl6552_data.ep = bl6552_cal_ep_sum(bl6552_reg_all.reg_cfp_cnt.val) 		* bl6552_data.factor_p_sum	+ bl6552_data.ep_init

        bl6552_data.ep_init_a = bl6552_data.ep_a
        bl6552_data.ep_init_b = bl6552_data.ep_b
        bl6552_data.ep_init_c = bl6552_data.ep_c
        bl6552_data.ep_init = bl6552_data.ep



        --功率因数转换
        bl6552_read_reg(bl6552_reg_all.reg_pf_a)
        bl6552_read_reg(bl6552_reg_all.reg_pf_b)
        bl6552_read_reg(bl6552_reg_all.reg_pf_c)
        bl6552_read_reg(bl6552_reg_all.reg_pf)
        
        bl6552_data.pf_a = bl6552_cal_pf(bl6552_reg_all.reg_pf_a.val)
        bl6552_data.pf_b = bl6552_cal_pf(bl6552_reg_all.reg_pf_b.val)
        bl6552_data.pf_c = bl6552_cal_pf(bl6552_reg_all.reg_pf_c.val)
        bl6552_data.pf = bl6552_cal_pf(bl6552_reg_all.reg_pf.val)


        bl6552_read_reg(bl6552_reg_all.reg_status1)
        log.info("phase_loss_read ","reg_status1:",bl6552_reg_all.reg_status1.val)

        local status = bl6552_reg_all.reg_status1.val & 0x07
        if 0x00 ~= status then
            if status ~= bl6552_data.phase_loss then
                bl6552_data.phase_loss = status
                bl6552_data.phase_loss_time = os.time()
            end
            log.info("phase_loss", "status:", bl6552_data.phase_loss, "  phase_loss_time:", bl6552_data.phase_loss_time)
        else
            bl6552_data.phase_loss = 0x00
            bl6552_data.phase_loss_time = 0
        end
        -- for k,v in pairs(bl6552_reg_all) do
        --     log.info("bl6552_read_Eregs", k, v.val, string.format( "%02x", v.val))
        -- end

        -- for k,v in pairs(bl6552_data) do

        --     log.info("bl6552_data", k, v)

        -- end
    end

end


local ep_save_step_ph = 0.5
local ep_save_step_sum = 1.5
local ep_init_a_last = 0
local ep_init_b_last = 0
local ep_init_c_last = 0
local ep_init_last = 0
local ep_a_last = 0
local ep_b_last = 0
local ep_c_last = 0
local ep_last = 0
local function ep_save_task()
    ep_init_a_last = bl6552_data.ep_init_a
    ep_init_b_last = bl6552_data.ep_init_b
    ep_init_c_last = bl6552_data.ep_init_c
    ep_init_last = bl6552_data.ep_init
    ep_a_last = bl6552_data.ep_a
    ep_b_last = bl6552_data.ep_b
    ep_c_last = bl6552_data.ep_c
    ep_last = bl6552_data.ep
    sys.taskInit(function()
        while true do
            sys.wait(60*1000)
            -- log.info("bl6552_ep_init_save", "ing...")
            if bl6552_data.ep_init_a - ep_init_a_last >= ep_save_step_ph then
                -- log.info("bl6552_ep_init_save", "ep_init_a", bl6552_data.ep_init_a)
                ep_init_a_last = bl6552_data.ep_init_a
                sys.publish("SAVE_ONE_DATA", "ep_init_a",bl6552_data.ep_init_a)
            end
            if bl6552_data.ep_init_b - ep_init_b_last >= ep_save_step_ph then
                -- log.info("bl6552_ep_init_save", "ep_init_b", bl6552_data.ep_init_b)
                ep_init_b_last = bl6552_data.ep_init_b
                sys.publish("SAVE_ONE_DATA", "ep_init_b",bl6552_data.ep_init_b)
            end
            if bl6552_data.ep_init_c - ep_init_c_last >= ep_save_step_ph then
                -- log.info("bl6552_ep_init_save", "ep_init_c", bl6552_data.ep_init_c)
                ep_init_c_last = bl6552_data.ep_init_c
                sys.publish("SAVE_ONE_DATA", "ep_init_c",bl6552_data.ep_init_c)
            end
            if bl6552_data.ep_init - ep_init_last >= ep_save_step_sum then
                -- log.info("bl6552_ep_init_save", "ep_init", bl6552_data.ep_init)
                ep_init_last = bl6552_data.ep_init
                sys.publish("SAVE_ONE_DATA", "ep_init",bl6552_data.ep_init)
            end
            
            if bl6552_data.ep_a - ep_a_last >= ep_save_step_ph then
                -- log.info("bl6552_ep_save", "ep_a", bl6552_data.ep_a)
                ep_init_a_last = bl6552_data.ep_a
                sys.publish("SAVE_ONE_DATA", "ep_a",bl6552_data.ep_a)
            end
            if bl6552_data.ep_b - ep_b_last >= ep_save_step_ph then
                -- log.info("bl6552_ep_save", "ep_b", bl6552_data.ep_b)
                ep_init_b_last = bl6552_data.ep_b
                sys.publish("SAVE_ONE_DATA", "ep_b",bl6552_data.ep_b)
            end
            if bl6552_data.ep_c - ep_c_last >= ep_save_step_ph then
                -- log.info("bl6552_ep_save", "ep_c", bl6552_data.ep_c)
                ep_init_c_last = bl6552_data.ep_c
                sys.publish("SAVE_ONE_DATA", "ep_c",bl6552_data.ep_c)
            end
            if bl6552_data.ep - ep_last >= ep_save_step_sum then
                -- log.info("bl6552_ep_save", "ep", bl6552_data.ep)
                ep_last = bl6552_data.ep
                sys.publish("SAVE_ONE_DATA", "ep",bl6552_data.ep)
            end
        end
    end
    )
end





local function bl6552_init()
    bl6552_busy = 1
    bl6552_uart_init()
    bl6552_config()


    -- if frist_init == 0  then
    --     bl6552_uart_reset()
    --     frist_init = 1
    -- end
    bl6552_busy = 0
end





local function get_data()
    return bl6552_data
end

local function get_one_data(name)
    return bl6552_data[name]
end

local function set_one_data(name,val)
    if nil ~= bl6552_data[name] then
        bl6552_data[name] = val
        sys.publish("SAVE_ONE_DATA",name,val)
    end
end




-- 复位恢复目前还有个问题，如果不是读后清零，那如果变化了系数，就会导致计量芯片的电量直接*系数，ep=ep_init+芯片电量
-- 断电重启会使计量芯片的电量清空，所以没有这个问题  ---所以现在用读后清零

--在复位的时候，ep_init恢复成ep_init 
local function recover_with_reset()

end

    
--在断电重启的时候需要恢复初始化电量ep  ep_init恢复成ep  计算电量时，ep=ep_init+芯片电量
local function recover_with_repower()
    bl6552_data.ep_init_a 		= bl6552_data.ep_a 		
    bl6552_data.ep_init_b 		= bl6552_data.ep_b 		
    bl6552_data.ep_init_c 		= bl6552_data.ep_c 		
    bl6552_data.ep_init 		= bl6552_data.ep 		
end



--[[
@description: 功能性重置  1:清空电量；2：系数复位
@return {*}
--]]
local function bl6552_rst(num)
    if 1 == num then
        set_one_data("ep_a",0)
        set_one_data("ep_b",0)
        set_one_data("ep_c",0)
        set_one_data("ep",0)
        set_one_data("ep_init_a",0)
        set_one_data("ep_init_b",0)
        set_one_data("ep_init_c",0)
        set_one_data("ep_init",0)
    elseif 2 == num then
        set_one_data("factor_pa",1)
        set_one_data("factor_pb",1)
        set_one_data("factor_pc",1)
        set_one_data("factor_p_sum",1)
    elseif 3 == num then

    end
end


local function read()
    sys.taskInit(function()
        local i = 10
        while true do
            while 1 == bl6552_busy do
                sys.wait(100)
                i = i-1
                if 0 == i then
                    break
                end
            end
            bl6552_busy = 1
            bl6552_read_Eregs() 
            bl6552_busy = 0
            sys.waitUntil("E_READ_ONCE",bl6552_reflash_time)
        end
    end)
end

local function init()
    sys.taskInit(function ()
        -- phase_loss_read_init()
        bl6552_init()
        ep_save_task()
        read()
    end)
end

local function bl6552_test()
    sys.taskInit(function()
        -- phase_loss_read_init()
        bl6552_init()
        -- bl6552_config()
        -- bl6552_write_reg_reset_test()
        while true do
            bl6552_read_Eregs()
            -- phase_loss_read()
            -- bl6552_read_reg_all()
            
            sys.wait(20000)
        end
    end)
end


bl6552 = {
    test = bl6552_test,
    init = init,
    get_data = get_data,
    get_one_data = get_one_data,
    set_one_data = set_one_data,
    recover_with_reset = recover_with_reset,
    recover_with_repower = recover_with_repower,
    rst = bl6552_rst,
}
return bl6552
