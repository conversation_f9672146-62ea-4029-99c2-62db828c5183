
local sys = require("sys")
local ntc = require("ntc")
local echip = require("bl6552")
local hlc668 = require("hlc668")
--硬件类型
HardWare = _G.PROJECT
SoftVersion = _G.VERSION

-- local uartid = uart.VUART_0 -- USB虚拟串口的固定id

--[[
@description: 条件返回，入参成立，return 指定字符串，否则返回ERR
@return {*}
--]]
local function at_ret_con( con ,val)
    if con ~= nil then
        if nil == val then
            return "ERR\r\n"
        elseif  "string" ~= type(val) then
            return tostring(val)..'\r\n'
        else
            return val..'\r\n'
        end 
    else
        return "ERR\r\n"
    end
end

--[[
@description: 清除某个存储值,注意，某些参数是即时储存的，这个函数主要用于清mqtt地址
@return {*}
--]]
local function set_fskv_del(str_recv)
    local pattern = "=([^,%s]+)%s*$"
    local name = string.match(str_recv, pattern)

    if name and (nil ~= fskv.get(name)) then
        fskv.del(name)
        return "OK\r\n"
    else
        return "ERR\r\n"
    end
end




--设置三元组
local function set_pnk_cb(str_recv)
    local pattern = "=([^,%s]+)%s*,%s*([^,%s]+)%s*,%s*([^,%s]+)%s*$"
    local p, n, s = string.match(str_recv, pattern)

    if p and n and s then
        fskv.set("product_key", p)
        fskv.set("device_name", n)
        fskv.set("device_secret", s)
        return "OK\r\n"
    else
        return "ERR\r\n"
    end
end

--获取三元组
local function get_pnk_cb(str_recv)
    local p, n, s = fskv.get("product_key"), fskv.get("device_name"), fskv.get("device_secret")
    
    if p and n and s then
        return p..','..n..','..s.."\r\n"
    else
        return "ERR\r\n"
    end
end

--设置端口和地址
local function set_hp_cb(str_recv)
    local pattern = "=%s*([^,%s]+)%s*,%s*([^,%s]+)%s*$"
    local h, p = string.match(str_recv, pattern)

    if h and p then
        fskv.set("mqtt_host", h)
        fskv.set("mqtt_port", p)      
        return "OK\r\n"
    else
        return "ERR\r\n"
    end
end

--获取端口和地址
local function get_hp_cb(str_recv)
    local h,p = fskv.get("mqtt_host"), fskv.get("mqtt_port")
    if h and p then
        return h..','..p..'\r\n'
    else
        return "ERR\r\n"
    end
end

--获取rsrp和rsrq以及注册状态
local function get_mobile_cb(str_recv)
    local h,p,s = mobile.rsrp(), mobile.rsrq(), mobile.status()
    if h and p and s then
        return tostring(h)..','..tostring(p)..','..tostring(s)..'\r\n'
    else
        return "ERR\r\n"
    end
end

--获取rssi
local function get_csq_cb(str_recv)
    local h = mobile.csq()
    return at_ret_con(h,h)
end


--获取电压
local function get_bat_cb(str_recv)
    adc.open(adc.CH_VBAT)
    local bat = adc.get(adc.CH_VBAT)
    adc.close(adc.CH_VBAT) 
    -- return at_ret_con(bat,tostring(bat/1000)) 
    if bat then
        return tostring(bat/1000)..'\r\n'
    else
        return "ERR\r\n"
    end
end

--查询软件版本
local function get_version_cb(str_recv)
    return HardWare..','..SoftVersion..'\r\n'
end


--[[
@description: 获取当前基站ID
@return {*}
--]]
local function get_mobile_eci(str_recv)
    local h = mobile.eci()
    
    return at_ret_con(h,h)
end

local function get_mobile_iccid()
    local h = mobile.iccid()
    return at_ret_con(h,h)
end
local function get_mobile_imei()
    local h = mobile.imei()
    return at_ret_con(h,h)
end
local function get_mobile_temp()
    local h = ntc.get_cpu_temp()
    return at_ret_con(h,h)
end
local function set_mobile_reboot()
    sys.publish("REBOOT_SAFE")
    return "OK\r\n"
end

local function get_ntc()
    local h = ntc.get_temp_internal()
    return at_ret_con(h,h)
end
local function get_ntc0()
    local h = ntc.get_temp_external()
    return at_ret_con(h,h)
end


local function get_echip_ca()
    local h = echip.get_one_data("ia")
    return at_ret_con(h,h)
end

local function get_echip_cb()
    local h = echip.get_one_data("ib")
    return at_ret_con(h,h)
end

local function get_echip_cc()
    local h = echip.get_one_data("ic")
    return at_ret_con(h,h)
end

local function get_echip_va()
    local h = echip.get_one_data("ua")
    return at_ret_con(h,h)
end

local function get_echip_vb()
    local h = echip.get_one_data("ub")
    return at_ret_con(h,h)
end

local function get_echip_vc()
    local h = echip.get_one_data("uc")
    return at_ret_con(h,h)
end

local function get_echip_pa()
    local h = echip.get_one_data("pa")
    return at_ret_con(h,h)
end

local function get_echip_pb()
    local h = echip.get_one_data("pb")
    return at_ret_con(h,h)
end

local function get_echip_pc()
    local h = echip.get_one_data("pc")
    return at_ret_con(h,h)
end


local function get_echip_p()
    local h = echip.get_one_data("p")
    return at_ret_con(h,h)
end

local function get_echip_epa()
    local h = echip.get_one_data("ep_a")
    return at_ret_con(h,h)
end
local function get_echip_epb()
    local h = echip.get_one_data("ep_b")
    return at_ret_con(h,h)
end
local function get_echip_epc()
    local h = echip.get_one_data("ep_c")
    return at_ret_con(h,h)
end

local function get_echip_ep()
    local h = echip.get_one_data("ep")
    return at_ret_con(h,h)
end

local function get_echip_fpa()
    local h = echip.get_one_data("factor_pa")
    return at_ret_con(h,h)
end

local function get_echip_fpb()
    local h = echip.get_one_data("factor_pb")
    return at_ret_con(h,h)
end

local function get_echip_fpc()
    local h = echip.get_one_data("factor_pc")
    return at_ret_con(h,h)
end

local function get_echip_fp()
    local h = echip.get_one_data("factor_p_sum")
    return at_ret_con(h,h)
end

-- local function get_echip_ep_cs_min()
--     local h = echip.get_one_data("ep_change_save_min")
--     return at_ret_con(h,h)
-- end



local function get_echip_freq()
    local h = echip.get_one_data("freq")
    return at_ret_con(h,h)
end

local function get_echip_phl()
    local h = echip.get_one_data("phase_loss")
    return at_ret_con(h,h)
end

local function get_echip_phl_time()
    local h = echip.get_one_data("phase_loss_time")
    return at_ret_con(h,h)
end

local function get_rf_code(str_recv)
    local h = ac_status.code.val
    return at_ret_con(h,h)
end

local function get_rf_temp(str_recv)
    local h = ac_status.temp.val
    return at_ret_con(h,h)
end

local function get_rf_mode(str_recv)
    local h = ac_status.mode.val
    return at_ret_con(h,h)
end

local function get_rf_fans(str_recv)
    local h = ac_status.fan.val
    return at_ret_con(h,h)   
end

local function get_rf_fanm(str_recv)
    local h = ac_status.fan_type.val
    return at_ret_con(h,h)   
end

local function get_ac_crp()
    local h = ac_status.c_run_power.val
    return at_ret_con(h,h) 
end

local function get_ac_rp()
    local h = ac_status.run_power.val
    return at_ret_con(h,h) 
end

local function get_ac_st(str_recv)
    local h = ac_status.runningstatus.val
    return at_ret_con(h,h) 
end

local function get_ac_cst(str_recv)
    local h = ac_status.comp_status.val
    return at_ret_con(h,h) 
end




--[[
@description: 获取空调开机总时长 单位h
@return {*}
--]]
local function get_ac_rt(str_recv)
    local h = ac_status.using_time.val
    return at_ret_con(h,h/3600) 
end
--[[
@description: 获取空调压缩机总时长 单位h
@return {*}
--]]
local function get_ac_crt(str_recv)
    local h = ac_status.comp_time.val
    return at_ret_con(h,h/3600) 
end

--[[
@description: 获取压缩机最后的连续运行时长 单位min
@return {*}
--]]
local function get_ac_crtl(str_recv)
    local h = ac_status.comp_running_time.val
    return at_ret_con(h,h/60) 
end

--[[
@description: 解析字符串为1个数字
@return {成功返回数字，失败返回false}
--]]
local function ret_number(str_recv)
    local pattern = "=%s*([^,%s]+)%s*$"
    local h = string.match(str_recv, pattern)
    -- log.info("ret_number",h)
    if nil ~= h then
        -- body
        local num = tonumber(h)
        if nil ~= num then
            return num
        else
            return false
        end
    else
        return false
    end
end

-- local function set_echip_ep_cs_min(str_recv)
--     local num = ret_number(str_recv)
--     if num then
--         echip.set_one_data("ep_change_save_min",num)  
--         return "OK\r\n"
--     else
--         return "ERR\r\n"
--     end
-- end

local function set_echip_epa(str_recv)
    local num = ret_number(str_recv)
    if num then
        echip.set_one_data("ep_a",num)   
        echip.set_one_data("ep_init_a",num)  
        return "OK\r\n"
    else
        return "ERR\r\n"
    end
end

local function set_echip_epb(str_recv)
    local num = ret_number(str_recv)
    if num then
        echip.set_one_data("ep_b",num)  
        echip.set_one_data("ep_init_b",num)  
        return "OK\r\n"
    else
        return "ERR\r\n"
    end
end

local function set_echip_epc(str_recv)
    local num = ret_number(str_recv)
    if num then
        echip.set_one_data("ep_c",num)  
        echip.set_one_data("ep_init_c",num)  
        return "OK\r\n"
    else
        return "ERR\r\n"
    end
end

local function set_echip_ep(str_recv)
    local num = ret_number(str_recv)
    if num then
        echip.set_one_data("ep",num)  
        echip.set_one_data("ep_init",num)  
        return "OK\r\n"
    else
        return "ERR\r\n"
    end
end




local function set_echip_fpa(str_recv)
    local num = ret_number(str_recv)
    if num then
        echip.set_one_data("factor_pa",num) 
        return "OK\r\n" 
    else
        return "ERR\r\n"
    end
end

local function set_echip_fpb(str_recv)
    local num = ret_number(str_recv)
    if num then
        echip.set_one_data("factor_pb",num) 
        return "OK\r\n" 
    else
        return "ERR\r\n"
    end
end

local function set_echip_fpc(str_recv)
    local num = ret_number(str_recv)
    if num then
        echip.set_one_data("factor_pc",num) 
        return "OK\r\n" 
    else
        return "ERR\r\n"
    end
end

local function set_echip_fp(str_recv)
    local num = ret_number(str_recv)
    if num then
        echip.set_one_data("factor_p_sum",num) 
        return "OK\r\n" 
    else
        return "ERR\r\n"
    end
end


local function set_erst(str_recv)
    local num = ret_number(str_recv)
    if num then
        echip.rst(num)     
        return "OK\r\n"
    else
        return "ERR\r\n"
    end
    
end



local rf_cmd = {
   ["open"] = 1,
   ["openm"] = 2,
   ["mode"] = 3,
   ["temp"] = 4,
   ["fans"] = 5,
   ["fanm"] = 6,
   ["code"] = 7,
   ["match"] = 8,

}


local function set_rf_open(str_recv)
    local num = ret_number(str_recv)   
    if num then
        local ret = hlc668.rf_cmd(rf_cmd["open"],num)  
        if true == ret then
            return "OK\r\n"
        else
            return "ERR\r\n"
        end
    else
        return "ERR\r\n"
    end
end

local function set_rf_openm(str_recv)

    local pattern = "=(%d+),(%d+)"
    local mode, temp = string.match(str_recv, pattern)
    if mode and temp then
        local ret = hlc668.rf_cmd(rf_cmd["openm"],tonumber(mode),tonumber(temp))     
        if true == ret then
            return "OK\r\n"
        else
            return "ERR\r\n"
        end
    else
        return "ERR\r\n"
    end
end

local function set_rf_mode(str_recv)
    local num = ret_number(str_recv)   
    if num then
        local ret = hlc668.rf_cmd(rf_cmd["mode"],num)  
        if true == ret then
            return "OK\r\n"
        else
            return "ERR\r\n"
        end
    else
        return "ERR\r\n"
    end
end

local function set_rf_temp(str_recv)
    local num = ret_number(str_recv)   
    if num then
        local ret = hlc668.rf_cmd(rf_cmd["temp"],num)  
        if true == ret then
            return "OK\r\n"
        else
            return "ERR\r\n"
        end
    else
        return "ERR\r\n"
    end
end

local function set_rf_fans(str_recv)
    local num = ret_number(str_recv)   
    if num then
        local ret = hlc668.rf_cmd(rf_cmd["fans"],num)  
        if true == ret then
            return "OK\r\n"
        else
            return "ERR\r\n"
        end
    else
        return "ERR\r\n"
    end
end

local function set_rf_fanm(str_recv)
    local num = ret_number(str_recv)   
    if num then
        local ret = hlc668.rf_cmd(rf_cmd["fanm"],num)  
        if true == ret then
            return "OK\r\n"
        else
            return "ERR\r\n"
        end
    else
        return "ERR\r\n"
    end
end

local function set_rf_code(str_recv)
    local num = ret_number(str_recv)   
    if num then
        local ret = hlc668.rf_cmd(rf_cmd["code"],num)  
        if true == ret then
            return "OK\r\n"
        else
            return "ERR\r\n"
        end
    else
        return "ERR\r\n"
    end
end

local function set_rf_match(str_recv)
    local num = ret_number(str_recv)   
    if num then
        local ret = hlc668.rf_cmd(rf_cmd["match"],1)  
        if true == ret then
            return "OK\r\n"
        else
            return "ERR\r\n"
        end
    else
        return "ERR\r\n"
    end
end

local function set_ac_crp(str_recv)
    local num = ret_number(str_recv)   
    if num then
        set_c_run_power(num)
        return "OK\r\n"
    else
        return "ERR\r\n"
    end
end

local function set_ac_rp(str_recv)
    local num = ret_number(str_recv)   
    if num then
        set_run_power(num)
        return "OK\r\n"
    else
        return "ERR\r\n"
    end
end

local function set_rst(str_recv)
    local num = ret_number(str_recv)   
    if num then
        sys.publish("RESET_PARAMS",num)
        sys.waitUntil("RESET_PARAMS_DONE",2000)
        sys.publish("REBOOT_SAFE")
        return "OK\r\n"
    else
        return "ERR\r\n"
    end
end

-- local function get_read_all()
    
--     local str = json.encode(ac_status)
--     if str then
--         return str
--     else
--         return "ERR\r\n"
--     end
    
-- end


local user_at_cmd_table = {

    ["+MOB?"] = get_mobile_cb,       -- 获取原始信号强度   获取rsrp和rsrq以及注册状态
    ["+CSQ?"] = get_csq_cb,       -- 获取信号强度  xx,99
    ["+ICCID?"] = get_mobile_iccid,     -- 获取卡号
    ["+IMEI?"] = get_mobile_imei,      -- 获取模组号
    ["+ECI?"] = get_mobile_eci,     --获取当前基站ID
    ["+AIRT?"] = get_mobile_temp,      -- 获取4G模组温度
    ["+AIRV?"] = get_bat_cb,     -- 获取4G模组电源电压  

    ["+FSKVDEL="] = set_fskv_del,       -- 设置三元组
    ["+PNK="] = set_pnk_cb,       -- 设置三元组
    ["+PNK?"] = get_pnk_cb,       -- 获取三元组
    ["+HP="] = set_hp_cb,        -- 设置MQTT地址和端口
    ["+HP?"] = get_hp_cb,        -- 获取MQTT地址和端口
    ["+VER?"] = get_version_cb,       -- 获取软件版本
    ["+REBOOT="] = set_mobile_reboot,     -- 重启

    ["+NTC?"] = get_ntc,       -- 获取内部NTC温度
    ["+NTC0?"] = get_ntc0,      -- 获取外部NTC温度NTC0        

    ["+ECA?"] = get_echip_ca,        -- 获取电流
    ["+ECB?"] = get_echip_cb,        -- 获取电流
    ["+ECC?"] = get_echip_cc,        -- 获取电流

    ["+EVA?"] = get_echip_va,        -- 获取电压
    ["+EVB?"] = get_echip_vb,        -- 获取电压
    ["+EVC?"] = get_echip_vc,        -- 获取电压

    ["+EPA?"] = get_echip_pa,        -- 获取功率
    ["+EPB?"] = get_echip_pb,        -- 获取功率
    ["+EPC?"] = get_echip_pc,        -- 获取功率
    ["+EP?"] = get_echip_p,         -- 获取功率

    ["+EEPA?"] = get_echip_epa,       -- 获取电量
    ["+EEPB?"] = get_echip_epb,       -- 获取电量
    ["+EEPC?"] = get_echip_epc,       -- 获取电量
    ["+EEP?"] = get_echip_ep,       -- 获取电量

    ["+EFPA?"] = get_echip_fpa,      -- 获取A相功率系数
    ["+EFPB?"] = get_echip_fpb,      -- 获取B相功率系数
    ["+EFPC?"] = get_echip_fpc,      -- 获取C相功率系数
    ["+EFP?"] = get_echip_fp,           -- 获取合相功率系数

    ["+EF?"] = get_echip_freq,      -- 获取频率
    ["+EPHL?"] = get_echip_phl,      -- 获取缺相状态
    ["+EPHLT?"] = get_echip_phl_time,      -- 获取缺相状态
    

    -- ["+EEPCSM?"] = get_echip_ep_cs_min, -- 获取电量保存颗粒度
    -- ["+EEPCSM="] = set_echip_ep_cs_min, -- 设置电量保存颗粒度

    ["+EEPA="] = set_echip_epa,      -- 设置电量,X为设置的初始电量值
    ["+EEPB="] = set_echip_epb,      -- 设置电量,X为设置的初始电量值
    ["+EEPC="] = set_echip_epc,      -- 设置电量,X为设置的初始电量值
    ["+EEP="] = set_echip_ep,      -- 设置电量,X为设置的初始电量值

    ["+EFPA="] = set_echip_fpa,      -- 设置A相功率系数
    ["+EFPB="] = set_echip_fpb,      -- 设置B相功率系数
    ["+EFPC="] = set_echip_fpc,      -- 设置C相功率系数
    ["+EFP="] = set_echip_fp,      -- 设置合相功率系数

   

    ["+ERST="] = set_erst,
    -- ["+ERST=1"] = ,     -- 无功能  
    -- ["+ERST=2"] = ,    -- 重置电流、电压系数为1   
    -- ["+ERST=3"] = ,    -- 重置电量  
    
    
    ["+RFCODE?"] = get_rf_code,    -- 获取码库ID    
    ["+RFCODE="] = set_rf_code,    -- 设置码库ID    
    ["+RFOPEN="] = set_rf_open,    -- 发送开机命令  0关机  1开机    
    ["+RFOPENM="] = set_rf_openm,   -- 模式开机命令，2个入参，模式、温度  0自动 1制冷默认26度；2除湿；3送风默认20度；4制热默认20度；    
    ["+RFTEMP?"] = get_rf_temp,    -- 获取空调设定温度    
    ["+RFTEMP="] = set_rf_temp,    -- 设置空调设定温度    
    ["+RFMODE="] = set_rf_mode,    -- 设置空调模式   0-自动，1-制冷，2-除湿，3-送风，4-制热    
    ["+RFMODE?"] = get_rf_mode,    -- 获取空调模式
    ["+RFFANS="] = set_rf_fans,     -- 设置空调风速  0-自动，1-低，2-中，3-高    
    ["+RFFANS?"] = get_rf_fans,    -- 获取空调风速
    ["+RFFANM="] = set_rf_fanm,          --设置扫风模式
    ["+RFFANM?"] = get_rf_fanm,         --获取扫风模式
    ["+RFMATCH="] = set_rf_match,   -- 设置进入匹配模式

    ["+ACRP="] = set_ac_rp,           -- 设置开机运行功率阈值
    ["+ACRP?"] = get_ac_rp,      -- 获取开机运行功率阈值
    ["+ACCRP="] = set_ac_crp,          -- 设置压缩机运行功率阈值
    ["+ACCRP?"] = get_ac_crp,     -- 获取压缩机运行功率阈值
    ["+ACCST?"] = get_ac_cst,     -- 获取压缩机状态
    ["+ACST?"] = get_ac_st,      -- 获取开机状态
    ["+ACCRT?"] = get_ac_crt,     -- 获取压缩机总运行时间
    ["+ACCRTL?"] = get_ac_crtl,      -- 获取压缩机本次运行时长
    ["+ACRT?"] = get_ac_rt,      -- 获取空调总运行时间
    ["+RST="] = set_rst,       -- 1重置设备统计、阈值 
    -- ["+RALL="] = get_read_all,   --一次读取ac_status所有参数
}




--处理PC 发过来的AT 指令
local function app_procmd(str_recv)
    log.info("str_recv------------", str_recv)
    local str_rsp
    -- local str_upper = string.upper(str_recv)
    local prefix = string.match(str_recv, "[aA][tT](%+%u+[%u%d])[%s*]")
    if  nil == prefix then
        prefix = string.match(str_recv, "[aA][tT](%+%u+[%u%d]+[%?=%s*])")
    end
    log.info("app_procmd",str_recv ,prefix)
    
    if prefix ~=nil then
        for k, v in pairs(user_at_cmd_table) do
            if k == prefix then
                str_rsp = v(str_recv)
                return str_rsp
                -- break
            end  
        end
        
    end
    return "ERR\r\n"
    
end




at_exec = {
    app_procmd = app_procmd
}

return at_exec

