
EQUIPS = {}

local EQUIP_DATA_TYPE = {["UnSigned"]="0", ["Signed"]="1", ["Float"]="5", ["FloatInverse"]="6", ["Int32"]="3", ["Int32Inverse"]="4", ["Bit"]="2"}

--十进制整数转二进制
function byte2bin(n)
    local t = {}
    for i=15,0,-1 do
      t[#t+1] = math.floor(n / 2^i)
      n = n % 2^i
    end
    return t
end

--判断是否为数组
function isDigital(s)
    local n = string.byte(s)
    if n >= 48 and n <= 58 then
        return true
    else
        return false
    end
end

--获取第n个元素位置
function getposition(src, k, num)
    local position = nil
    local j = 0
    for i=1,#src do
        if string.sub(src,i,i) == k then
            j = j + 1
            if j == num then
                position = i + 1
                break
            end
        end
    end

    return string.sub(src, position)
end

--处理设备属性列表
local function equip_add_properties(properties)
    local property_table = {}
    for k,v in pairs(properties) do
        local property = {}
        local r1 = string.split(v, "-")
        if #r1 >= 6 then
            property.index = r1[4]
            property.datatype = r1[5]
            local formula = getposition(v, "-", 5)
            local r2 = string.split(formula, "|")
            property.formulatype = r2[1]
            if property.formulatype == "X" then
                property.factor = 1
                property.offset = 0
                if #r2 == 3 then
                    property.factor = tonumber(r2[2])
                    property.offset = tonumber(r2[3])
                end
            elseif property.formulatype == "B" then
                property.bit_table = {}
                for i =2, #r2 do
                    property.bit_table[i-1] = tonumber(r2[i])
                end
            elseif property.formulatype == "M" then
                property.map_table = {}
                for i =2, #r2 do
                    local r3 = string.split(r2[i], ";")
                    local index = tonumber(r3[1])
                    local value = tonumber(r3[2])
                    property.map_table[index] = value
                end
            elseif property.formulatype == "E" then
                property.enum_table = {}
                for i =2, #r2 do
                    property.enum_table[i-1] = tonumber(r2[i])
                end
            end
        end
        property_table[k] = property
    end
    return property_table
end

--处理命令列表
local function equip_add_commands(commands)
    local cmd_table = {}
    for k,v in pairs(commands) do
        local property_num = 0
        local cmd = {}
        local r1 = string.split(v, "-")
        if #r1 >= 6 then
            cmd.action = tonumber(r1[2])
            cmd.addr = tonumber(r1[3])
            cmd.datatype = tonumber(r1[5])
            
            local formula = getposition(v, "-", 5)
            local r2 = string.split(formula, "|")

            -- local r2 = string.split(r1[6], "|")						
            cmd.formulatype = r2[1]
            if not isDigital(r2[#r2]) then
                property_num = 1
                cmd.prop_name = r2[#r2]
            end

            if cmd.formulatype == "X" then
                cmd.factor = 1
                cmd.offset = 0
                if #r2 >= 3 then
                    cmd.factor = tonumber(r2[2])
                    cmd.offset = tonumber(r2[3])
                end
            elseif cmd.formulatype == "M" then
                cmd.map_table = {}
                for i =2, #r2-property_num do
                    local r3 = string.split(r2[i], ";")
                    local index = tonumber(r3[1])
                    local value = tonumber(r3[2])
                    cmd.map_table[index] = value
                end
            end
        end
        log.info("equip_add_commands", k,cmd.action, cmd.addr, cmd.datatype, cmd.formulatype, cmd.factor, cmd.offset)
        cmd_table[k] = cmd
    end
    return cmd_table
end

--添加设备列表
local function equips_add(equips_config)
    for k,v in pairs(equips_config) do
        local equip = {}
        equip.projectId = v.projectId
        equip.equipId = v.equipId
        equip.properties = equip_add_properties(v.properties)
        equip.cmds = equip_add_commands(v.cmds)
        EQUIPS[equip.equipId] = equip
        break
    end
end

local function equips_data_to_resource(equips_data)
    log.info("equips_data_to_resource", equips_data)
    local need_update_name = {
        {equip_name = "mode",resource_name = "mode"},
        {equip_name = "temp_set_point",resource_name = "temp"},
        {equip_name = "fan_speed",resource_name = "fan"},
        -- {equip_name = "fan_type",resource_name = "fan_type"},
        {equip_name = "compressor_time",resource_name = "comp_time"},
        {equip_name = "using_time",resource_name = "using_time"},
        {equip_name = "ep_a",resource_name = "ep_a"},
        {equip_name = "ep_b",resource_name = "ep_b"},
        {equip_name = "ep_c",resource_name = "ep_c"},
        {equip_name = "ep",resource_name = "ep"}
    }
    for k,v in pairs(EQUIPS) do
        for index,val in pairs(equips_data) do
            if k == val.equipId then
                if nil ~= val.properties then
                    for index2,v2 in pairs(need_update_name) do
                        if nil ~= val.properties[v2.equip_name] and nil ~= ac_status[v2.resource_name] then
                            if index2 <=3  then  
                                if nil ~= v.properties[v2.equip_name]["factor"] and nil ~= v.properties[v2.equip_name]["offset"] and v.properties[v2.equip_name]["formulatype"] == "X" then
                                    ac_status[v2.resource_name].val =  (tonumber(val.properties[v2.equip_name])-v.properties[v2.equip_name]["offset"])
                                                                    /v.properties[v2.equip_name]["factor"]
                                end
                            end
                            if index2>3 then-- remind need compare values ,biger one will be us
                                if  tonumber(val.properties[v2.equip_name]) > ac_status[v2.resource_name].val then
                                    if nil ~= v.properties[v2.equip_name]["factor"] and nil ~= v.properties[v2.equip_name]["offset"] and v.properties[v2.equip_name]["formulatype"] == "X" then
                                        ac_status[v2.resource_name].val =  (tonumber(val.properties[v2.equip_name])-v.properties[v2.equip_name]["offset"])
                                                                    /v.properties[v2.equip_name]["factor"]
                                    end
                                end
                            end
                            log.info("equip_data_to_resource", v2.resource_name,tonumber(val.properties[v2.equip_name]), ac_status[v2.resource_name].val)
                        end
                    end
                end
            end
        end
    end
    sys.publish("AC_DATA_TO_CHIP")
end

--处理设备命令
local function equip_proc_command(params)
    local equip_id = params.equipId
    local cmd_name = params.cmdName
    local value = tonumber(params.cmdParam)

    if value == nil then
        return
    end

    local ret = -1
    if EQUIPS[equip_id] ~= nil then
        -- log.info("equip_proc_command", equip_id, cmd_name, value)
        if EQUIPS[equip_id]["cmds"] ~= nil then
            local cmd = EQUIPS[equip_id]["cmds"][cmd_name] 
            -- log.info("equip_proc_command", equip_id, cmd_name, value)
            if cmd ~= nil then
                if cmd.formulatype == "X" then
                    local true_value = value*cmd.factor + cmd.offset
                    ret = air_condition_proc_cmd(cmd.addr, true_value)
                elseif cmd.formulatype == "M" then
                    local true_value = value
                    if cmd.map_table[value] ~= nil then
                        true_value = cmd.map_table[value]
                        ret = air_condition_proc_cmd(cmd.addr, true_value)
                    end
                end
            end
        end
    end

    sys.publish("CMDREPLY", ret)
    sys.taskInit(
    function()
        for i=1,3 do  
            sys.wait(3000)
            sys.publish("UPLOAD_DATA")
        end
        sys.publish("UPLOAD_RESOURCE")
    end
)
end

--上报设备属性
local function equip_post_properties(property_pub)
    sys.publish("EQUIPDATA", property_pub)
end


--更新所有设备的属性
local function equips_update_properties(nodedata)
    local equips_pub = {}
    local i = 1
    equips_pub.method = "equipData"
    equips_pub.equipDatas = {}
    for k, v in pairs(EQUIPS) do
        local equip_pub = {}
        local property_pub = {}
        local post_flag = 0 
        for key, p in pairs(v.properties) do
            -- print("key = ", key)
            local index = tonumber(p.index)
            local value = nodedata[index+1]
            if value ~= nil then
                
                --数据处理，先通过公式修正
                local true_value = value
                if p.formulatype == "X" then
                    true_value = value*p.factor +  p.offset
                elseif p.formulatype == "B" then
                    local bin = byte2bin(value)
                    true_value = 0
                    for k2, v2 in pairs(p.bit_table) do
                        if bin[16-v2] == 1 then
                            true_value = 1
                            break
                        end
                    end
                elseif p.formulatype == "M" then
                    if p.map_table[value] ~= nil then
                        true_value = p.map_table[value]
                    end
                elseif p.formulatype == "E" then
                    true_value = 0
                    for k2, v2 in pairs(p.enum_table) do
                        if v2 == value then
                            true_value = 1
                            break
                        end
                    end
                end

                --数据处理，数据类型修正
                if p.datatype == EQUIP_DATA_TYPE["Bit"] then
                    if true_value > 0.5 then
                        p.value = 1
                    else
                        p.value = 0
                    end
                    --添加到属性列表中
                    property_pub[key] = tostring(p.value)
                elseif p.datatype == EQUIP_DATA_TYPE["UnSigned"] or p.datatype == EQUIP_DATA_TYPE["Signed"] then
                    p.value = math.modf(true_value)
                    property_pub[key] = tostring(p.value)
                else 
                    p.value = true_value
                    property_pub[key] = string.format("%.3f", p.value)
                end

                post_flag = 1
            end
        end

        --如果有属性更新,则发布设备属性消息
        if post_flag == 1 then
            v.timestamp = tostring(os.time())
            equip_pub.projectId = v.projectId
            equip_pub.equipId = v.equipId
            equip_pub.timestamp = v.timestamp
            equip_pub.properties = property_pub
            --将设备添加到数组中
            equips_pub.equipDatas[1] = equip_pub
            --发布设备属性
            equip_post_properties(equips_pub)
        end
    end
end


local equips = {
    equips_add = equips_add,
    equips_data_to_resource= equips_data_to_resource,
    equips_update_properties = equips_update_properties,
    equip_proc_command = equip_proc_command
}
return equips


