-- Desc: NTC温度传感器驱动

local sys = require "sys"

local ADC_ID = 1
local ADC_ID0 = 0
local Vbat = 3.746
local ntc_temp = 0.0    --这是内部的温度传感器
local ntc0_temp = 0.0   --0是外部的温度传感器
local cpu_temp = 0      --cpu的温度

local ntc_B = 3435
local ntc0_B = 3435




-- Temp_Tab_10K=                                                            
-- {
--     95.3370,92.6559,90.0580,87.5406,85.1009,82.7364,80.4445,78.2227,76.0689,73.9806,    ---20~-15.5
--     71.9558,69.9923,68.0881,66.2412,64.4499,62.7122,61.0264,59.3908,57.8038,56.2639,    ---15~-10.5
--     54.7694,53.3189,51.9111,50.5445,49.2178,47.9298,46.6792,45.4649,44.2856,43.1403,    ---10~-5.5
--     42.0279,40.9447,39.8978,38.8780,37.8873,36.9246,35.9892,35.0801,34.1965,33.3378,    ---5~-0.5
--     32.5030,31.6915,30.9026,30.1355,29.3896,28.6644,27.9590,27.2730,26.6058,25.9567,    --0~4.5
--     25.3254,24.7111,24.1135,23.5320,22.9661,22.4154,21.8795,21.3579,20.8502,20.3559,    --5~9.5
--     19.8747,19.4063,18.9502,18.5060,18.0735,17.6523,17.2421,16.8426,16.4534,16.0743,    --10~14.5
--     15.7049,15.3450,14.9944,14.6528,14.3198,13.9954,13.6792,13.3710,13.0705,12.7777,    --15~19.5
--     12.4922,12.2138,11.9425,11.6778,11.4198,11.1681,10.9227,10.6834,10.4499,10.2222,    --20~24.5
--     10.000,9.7833,9.5718,9.3655,9.1642,8.9677,8.7760,8.5889,8.4063,8.2281,    --25~29.5
--     8.0541,7.8842,7.7184,7.5565,7.3985,7.2442,7.0935,6.9463,6.8026,6.6622,    --30~34.5
--     6.5251,6.3912,6.2604,6.1326,6.0077,5.8858,5.7666,5.6501,5.5363,5.4251,    --35~39.5
--     5.3164,5.2102,5.1064,5.0049,4.9057,4.8088,4.7140,4.6213,4.5307,4.4421,    --40~44.5
--     4.3554,4.2707,4.1878,4.1068,4.0275,3.9500,3.8742,3.8000,3.7275,3.6565,    --45~49.5
--     3.5870,3.5190,3.4525,3.3875,3.3238,3.2615,3.2005,3.1408,3.0824,3.0252,    --50~54.5
--     2.9692,2.9144,2.8608,2.8082,2.7568,2.7065,2.6572,2.6089,2.5616,2.5153,    --55~59.4
--     2.4700,2.4255,2.3820,2.3394,2.2977,2.2568,2.2167,2.1775,2.1390,2.1013,    --60~64.5
--     2.0644,2.0282,1.9928,1.9580,1.9240,1.8906,1.8579,1.8258,1.7944,1.7636,    --65~69.5
--     1.7334,1.7037,1.6747,1.6462,1.6183,1.5910,1.5641,1.5378,1.5120,1.4867,    --70~74.5
--     1.4619,1.4375,1.4136,1.3902,1.3672,1.3447,1.3225,1.3008,1.2795,1.2586,    --75~79.5
--     1.2381,1.2180,1.1983,1.1789,1.1599,1.1412,1.1229,1.1050,1.0873,1.0700,    --80~84.5
--     1.0530,1.0363,1.0199,1.0038,0.9880,0.9725,0.9573,0.9424,0.9277,0.9133,    --85~89.5
--     0.8991,0.8852,0.8715,0.8581,0.8450,0.8320,0.8193,0.8068,0.7945,0.7825,    --90~94.5
--     0.7707,0.7590,0.7476,0.7364,0.7253,0.7145,0.7038,0.6933,0.6831,0.6729,    --95~99.5
--     0.6630    --100
-- }

--根据阻值计算温度
local function get_temp_by_calc(res,b_val)  
    local temp = res/10
    temp = math.log(temp)
    temp = temp/b_val
    temp = temp + 1/298.15
    temp = 1/temp
    temp = temp - 273.15       
    -- log.info("cal",temp, res)
    return temp
end



--获取采集的温度
function Get_Temp()
    local adcval,voltval = adc.read(ADC_ID)
    local value = voltval/1000
    local Resistance = 49.9*(value/(Vbat-value))
    local Temperature = get_temp_by_calc(Resistance,ntc_B)
    --[[
    for k, v in pairs(Temp_Tab_10K) do 
        if Resistance> v then
            Temperature = (((k-1)-40)/2) 
            break;
        end     
    end ]]
    ntc_temp = Temperature
    -- log.info("Get_Temp",adcval, voltval, Temperature)
    return  Temperature
end 

function Get_Temp0()
    local adcval,voltval = adc.read(ADC_ID0)
    local value = voltval/1000
    local Resistance = 49.9*(value/(Vbat-value))
    local Temperature = get_temp_by_calc(Resistance,ntc0_B)

    ntc0_temp = Temperature
    -- log.info("Get_Temp0",adcval, voltval, Temperature)
    return  Temperature
end 

-- local function get_vbat()
--     adc.open(adc.CH_VBAT)
--     local vbat = adc.get(adc.CH_VBAT)
--     Vbat = vbat/1000
--     adc.close(adc.CH_VBAT)
--     log.info("get_vbat ", vbat)
--     return vbat
-- end

local function Get_CPU_Temp()
    adc.open(adc.CH_CPU)
    local temp = adc.get(adc.CH_CPU)/1000
    -- log.info("get_cpu_temp ",temp)
    adc.close(adc.CH_CPU)
    cpu_temp = temp
    return temp
end

-- adc.open(3)
--sys.timerLoopStart(Get_Temp,1000)
local function ntc_init()
    adc.open(ADC_ID)
    adc.open(ADC_ID0)
    Get_Temp()
    Get_Temp0()
    Get_CPU_Temp()
    -- sys.timerLoopStart(Get_Temp , 15000)
    -- sys.timerLoopStart(Get_Temp0 , 14000)
    -- sys.timerLoopStart(get_vbat, 1000)
    -- sys.timerLoopStart(get_cpu_temp, 20000)
end


-- local function test()
--     ntc_init()    
--     -- sys.timerLoopStart(Get_Temp , 1000)
--     -- sys.timerLoopStart(Get_Temp0 , 1000)
--     -- sys.timerLoopStart(get_vbat, 1000)
--     -- sys.timerLoopStart(get_cpu_temp, 1000)
--     sys.taskInit(function ()
--         while true do
--             Get_Temp()
--             Get_Temp0()
--             get_vbat()
--             get_cpu_temp()
--             sys.wait(15000)
--         end
        

--     end)
-- end

local function get_temp_internal()
    Get_Temp()
    return ntc_temp
end

local function get_temp_external()
    Get_Temp0()
    return ntc0_temp
end

local function get_cpu_temp()
    Get_CPU_Temp()
    return cpu_temp
end

-- local function get_last_temp()
--     return last_temp
-- end
-- local function set_last_temp(temp)
--     last_temp = temp
-- end

local ntc = {
    init = ntc_init,
    -- test = test,
    get_temp_external = get_temp_external,
    get_temp_internal = get_temp_internal,
    get_cpu_temp      = get_cpu_temp,
    -- to_temp_outside = to_temp_outside,   --外部传感器不需要这种拟合算法
    -- get_last_temp = get_last_temp,
    -- set_last_temp = set_last_temp
}

return ntc