--[[
Author: <PERSON>
Date: 2025-08-22 15:36:53
LastEditors: <PERSON>Yang
LastEditTime: 2025-08-22 15:37:39
FilePath: \sc-a-g02\relay.lua
Description: 

--]]


local RELAY_INA_GPIO = 24
local RELAY_INB_GPIO = 22
local relay_init_flag = 0
local relay_status = 1
local function relay_status_save() 
     sys.publish("SAVE_ONE_DATA", 'relay_status',relay_status)
end
local function relay_status_load() 
    local value = fskv.get("relay_status")
        if nil ~= value then
            relay_status = value
            relay_status_save()
        end
end

local function relay_init()
    gpio.setup(RELAY_INA_GPIO,0,gpio.PULLUP)
    gpio.setup(RELAY_INB_GPIO,0,gpio.PULLUP)
    relay_init_flag = 1
    relay_status_load()
end

local function relay_connect()
    --排除未初始化就调用，防止状态不能对齐
    if 0 == relay_init_flag then
        return
    end
    gpio.set(RELAY_INA_GPIO,0)
    gpio.set(RELAY_INB_GPIO,1)
    sys.wait(100)
    gpio.set(RELAY_INA_GPIO,0)
    gpio.set(RELAY_INB_GPIO,0)
    relay_status = 1
    relay_status_save()
end


local function relay_disconnect()
    if 0 == relay_init_flag then
        return
    end
    gpio.set(RELAY_INA_GPIO,1)
    gpio.set(RELAY_INB_GPIO,0)
    sys.wait(100)
    gpio.set(RELAY_INA_GPIO,0)
    gpio.set(RELAY_INB_GPIO,0)
    relay_status = 0
    relay_status_save()
end

local function relay_switch(status)
    if "number" ~= type(status) then
        return false
    end
    if 1 == status then
        relay_connect()
    elseif 0 == status then
        relay_disconnect()
    else
        return false
    end
    return true
end

local function relay_get_status()
    return relay_status
end

local function relay_toggle()
    if 1 == relay_status then
        relay_disconnect()
    elseif 0 == relay_status then
        relay_connect()
    end
end


local relay = {
    init = relay_init,
    switch = relay_switch,
    get_status = relay_get_status,
    toggle = relay_toggle
}
return relay