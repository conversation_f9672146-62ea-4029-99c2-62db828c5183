

local ir_chip = require "hlc668"
local e_chip = require "bl6552"
local ntc = require "ntc"
local key = require "key"
local v_uart = require  "v_uart"
local relay = require "relay"
ac_status = {
						-- 序号       值				存储时间间隔单位min		是否掉电存储				
						-- 存储时间间隔为0，表示不进行周期存储，自行存储
    power_status 		= {index = 1,  val = 0 },               	--电源状态
    runningstatus 		= {index = 2,  val = 0 },              	--运行状态
    comp_status 		= {index = 3,  val = 0 },            	    --压缩机状态
    mode       			= {index = 4,  val = 1 },                  --空调运行模式   
    temp       			= {index = 5,  val = 26},                  --空调设定温度   
    fan        			= {index = 6,  val = 0 },                  --风速
    fan_type   			= {index = 7,  val = 1 },                  --扫风开关
    box_temp    		= {index = 8,  val = 0.0 },              	--控制器内部温度
    indoor_temp 		= {index = 9,  val = 0.0 },                 --室内温度 回风
    humi       			= {index = 10, val = 0 },                 --湿度
    code       			= {index = 11, val = 0 },                  --码库
    comp_time 			= {index = 12, val = 0 },            		--压缩机总运行时间
    comp_running_time 	= {index = 13, val = 0 },       		    --压缩机本次运行的时间
    using_time 			= {index = 14, val = 0 },                  --空调总使用时间
    run_power 			= {index = 15, val = 20},                 --开机功率阈值
    c_run_power 		= {index = 16, val = 60},               	--压缩机运行功率阈值
	rssi				= {index = 17, val = 0 },               	--4G信号值
    ia 					= {index = 18, val = 0 },			--a相电流
    ib 					= {index = 19, val = 0 },			--b相电流
    ic 					= {index = 20, val = 0 },			--c相电流
    ua 					= {index = 21, val = 0 },			--a相电压
    ub 					= {index = 22, val = 0 },			--b相电压
    uc 					= {index = 23, val = 0 },			--c相电压	
    pa 					= {index = 24, val = 0 },			--a相有功功率
    pb 					= {index = 25, val = 0 },			--b相有功功率
    pc 					= {index = 26, val = 0 },			--c相有功功率
    p 					= {index = 27, val = 0 },			--合相有功功率
    ep_a 				= {index = 28, val = 0 },			--a相正向有功电量，
    ep_b 				= {index = 29, val = 0 },	        --b相正向有功电量，
    ep_c 				= {index = 30, val = 0 },			--c相正向有功电量，
    ep 					= {index = 31, val = 0 },         	--合相正向有功电量，
    ep_init_a 			= {index = 32, val = 0 },	        --a相正向有功初始电量，
    ep_init_b 			= {index = 33, val = 0 },	        --b相正向有功初始电量，
    ep_init_c 			= {index = 34, val = 0 },	        --c相正向有功初始电量，
    ep_init 			= {index = 35, val = 0 },          --合相正向有功初始电量，
    pf_a 				= {index = 36, val = 0 },			--a相功率因数
    pf_b 				= {index = 37, val = 0 },			--b相功率因数
    pf_c 				= {index = 38, val = 0 },			--c相功率因数
    pf 					= {index = 39, val = 0 },         --合相功率因数
	freq 				= {index = 40, val = 0 },			--频率
    factor_pa           = {index = 41, val = 1 },			--a相功率校正系数
    factor_pb           = {index = 42, val = 1 },			--b相功率校正系数
    factor_pc           = {index = 43, val = 1 },			--c相功率校正系数
    factor_p_sum        = {index = 44, val = 1 },			--合相相校正系数
    phase_loss 			= {index = 45, val = 0 },         --缺相告警 bit0 A相，bit1 B相，bit2 C相  1为缺相
    phase_loss_time 	= {index = 46, val = 0 },        	--缺相发生时的时间戳，正常时为0	
    relay_status 		= {index = 47, val = 1 }        	--继电器状态
    -- ep_change_save_min 	= {index = 47, val = 1 }        	--电量变化的存储的颗粒度默认1度电存储一次

}



local function ac_data_to_chip()
    
    local reason, slp_state, reason_detail = pm.lastReson() 
    log.info("开机原因",reason_detail)

    local ir_data = ir_chip.get_data()
    for k,v in pairs(ir_data) do
        if nil ~= ac_status[k] then
            ir_data[k] = ac_status[k].val
        end
    end


    local e_data = e_chip.get_data()
    for k,v in pairs(e_data) do
        if nil ~= ac_status[k] then
            e_data[k] = ac_status[k].val
        end
    end

    -- 由于电量每次都读后清零，所以每次都按重启的规格恢复电量（ep_init恢复为ep）
    e_chip.recover_with_repower() 
    
end

sys.subscribe("AC_DATA_TO_CHIP", ac_data_to_chip)


--设置开关功率阈值
function set_run_power(value)
    sys.publish("SAVE_ONE_DATA", 'run_power',value)
    -- log.info("set run power", value)
    return true
end

--设置压缩机开关功率阈值
function set_c_run_power(value)
    sys.publish("SAVE_ONE_DATA", 'c_run_power',value)
    -- log.info("set c run power", value)
    return true
end



function reset_params(num)
    if 1 == num then
        ac_status.comp_time.val = 0
        -- ac_status.comp_running_time.val = 0
        ac_status.using_time.val = 0
    elseif 2 == num then
        ac_status.run_power.val = 20
        ac_status.c_run_power.val = 60

    elseif 3 == num then
        ac_status.ep_a.val = 0
        ac_status.ep_b.val = 0
        ac_status.ep_c.val = 0
        ac_status.ep.val = 0
        ac_status.ep_init_a.val = 0
        ac_status.ep_init_b.val = 0
        ac_status.ep_init_c.val = 0
        ac_status.ep_init.val = 0
    elseif 255 == num then
        ac_status.comp_time.val = 0
        ac_status.comp_running_time.val = 0
        ac_status.using_time.val = 0

        ac_status.run_power.val = 20
        ac_status.c_run_power.val = 60
        
        ac_status.ep_a.val = 0
        ac_status.ep_b.val = 0
        ac_status.ep_c.val = 0
        ac_status.ep.val = 0
        ac_status.ep_init_a.val = 0
        ac_status.ep_init_b.val = 0
        ac_status.ep_init_c.val = 0
        ac_status.ep_init.val = 0
    end
        
    sys.publish("SAVE_ONE_DATA", 'run_power', ac_status.run_power.val)
    sys.publish("SAVE_ONE_DATA", 'c_run_power', ac_status.c_run_power.val)
        
    log.info("reset_params", "store ac_data")
    sys.publish("RESET_PARAMS_DONE")
    return true
end

    sys.subscribe("RESET_PARAMS", reset_params)

-- 命令代号
-- 1 继电器
-- 2 开关机
-- 3 制冷开机
-- 4 制热开机
-- 5 模式
-- 6 温度设定
-- 7 风速
-- 8 自动模式开机
-- 9 除湿开机
-- 10 一键匹配
-- 11 设置码库
-- 12 送风开机
-- 13 发送通用命令
-- 14 设置电能、功率比例因数-三相只能通过AT指令设置，此功能无用
-- 15 设置开机功率
-- 16 设置压缩机运行功率
-- 17 获取芯片版本号
-- 18 恢复出厂设置

-- local cmd_table = {
--     relay_switch,
--     bsp_ir_chip_set_air_cond_power,
--     bsp_ir_chip_set_air_cond_power_on_cool,
--     bsp_ir_chip_set_air_cond_power_on_heat,
--     bsp_ir_chip_set_air_cond_mode,
--     bsp_ir_chip_set_air_cond_temp,
--     bsp_ir_chip_set_air_cond_fan,
--     bsp_ir_chip_set_air_cond_power_on_auto,
--     bsp_ir_chip_set_air_cond_power_on_humi,
--     bsp_ir_chip_one_step_match,
--     bsp_ir_chip_set_code,
--     bsp_ir_chip_set_air_cond_power_on_fan,
--     bsp_ir_chip_send_common_command,
--     set_power_factor,
--     set_run_power,
--     set_c_run_power,
--     bsp_ir_get_chip_version,
--     reset_params,
-- }
   

local cmd_table = {
    v_uart.set_status,
    
    -- 2  open or close
    function (val)
        return ir_chip.rf_cmd(1,val)
    end,
    -- 3 cool-open and set temperature
    function (temp)
        return ir_chip.rf_cmd(2,1,temp)
    end,
    -- 4 heat-open and set temperature
    function (temp)
        return ir_chip.rf_cmd(2,4,temp)
    end,
    -- 5 set mode
    function (mode)
        return ir_chip.rf_cmd(3,mode)
    end,
    -- 6 set temperature
    function (temp)
        return ir_chip.rf_cmd(4,temp)
    end,
    
    --7 set fan_speed
    function (fan_speed)
        return ir_chip.rf_cmd(5,fan_speed)
    end,
    
    -- 8 auto-open and set temperature
    function (temp)
        return ir_chip.rf_cmd(2,0,temp)
    end,
    -- 9 humi-open and set temperature
    function (temp)
        return ir_chip.rf_cmd(2,2,temp)
    end,
    -- 10 set rf one_step_match
    function (val)
        return ir_chip.rf_cmd(8,val)
    end,
    
    -- 11 set rf code
    function (code)
        return ir_chip.rf_cmd(7,code)
    end,

    -- 12 fan-open 
    function (temp)
        return ir_chip.rf_cmd(2,3,temp)
    end,
    
    -- 13
    bsp_ir_chip_send_common_command,
    
    -- 14 nil
    function ()
        -- 原函数不再需要，这里留空
        return false
    end,
    -- 15 set _run_power
    set_run_power,
    -- 16 set c_run_power
    set_c_run_power,
    
    -- 17 useless
    bsp_ir_get_chip_version,
    
    -- 18 reset params  input 255,will all recover to defaults 
    reset_params,

    -- 19 set relay status
    relay.switch,
}


--处理设备命令
function air_condition_proc_cmd(addr, value)
    local ret = -1
    local result = false
    log.info("air_condition_proc_cmd", addr, value)
    if cmd_table[addr] ~= nil then
        for i=1,1 do
            result = cmd_table[addr](value)
            if result == true then
                break
            end
        end
    end
    if result == true then
        ret = 0
    else
        ret = -1
    end
    log.info("air_condition_proc_cmd", addr, value, result, ret)
    return ret
end




local function ac_data_recover()
    sys.wait(3000)
    log.info("fdb", "all data read start")
    local iter = fskv.iter()
    if iter then
        while 1 do
            local k = fskv.next(iter)
            if not k then
                break
            end
            log.info("fdb ", k, " value ", fskv.get(k))
        end
    end

    for k, v in pairs(ac_status) do
        local value = fskv.get(k)
        if nil ~= value then
            ac_status[k].val= value
        end
        -- log.info("ac_data_recover", k, ac_status[k])
    end
    
    ac_data_to_chip()
end


local function init()
    e_chip.init()
    ir_chip.init()
    ntc.init()
    key.init()
    relay.init()
end

local function get_data()
    local e_data    = e_chip.get_data()
    for k,v in pairs(e_data) do
        if nil ~=  ac_status[k] then
            ac_status[k].val = v
        end
    end

    ac_status.box_temp.val      = ntc.get_temp_internal()
    ac_status.indoor_temp.val   = ntc.get_temp_external()

    local ir_data       = ir_chip.get_data()
    
    for k,v in pairs(ir_data) do
        if nil ~=  ac_status[k] then
            ac_status[k].val = v
        end
    end
end


local ac = {
    init = init,
    data_recover = ac_data_recover,
    get_data = get_data,
}
return ac