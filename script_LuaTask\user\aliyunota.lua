
--- 模块功能：阿里云物联网套件客户端OTA功能.
-- 目前固件签名算法仅支持MD5
-- @module aLiYunOta
-- <AUTHOR>
-- @license MIT
-- @copyright openLuat
-- @release 2018.04.16

local aliyun = require "aliyun"


local otafilePath = '/data.bin'
--gVersion：固件版本号字符串，如果用户没有调用本文件的setVer接口设置，则默认为_G.PROJECT.."_".._G.VERSION.."_"..sys.getcorever()
--gName：阿里云iot网站上配置的新固件文件下载后，在模块中的保存路径，如果用户没有调用本文件的setName接口设置，则默认为/luazip/update.bin
--gCb：新固件下载成功后，要执行的回调函数
local gVersion,gName,gCb = _G.PROJECT.."_".._G.VERSION.."_"..rtos.version(),otafilePath
local gFilePath,gFileSize
local processed = 0
local flowMd5
--productKey：产品标识
--deviceName：设备名称
local productKey,deviceName

--verRpted：版本号是否已经上报
local verRpted
local sConnected

--lastStep：最后一次上报的下载新固件的进度
local lastStep
--下载中标志
local downloading


local function fota_cb(ret)
    log.info("fota", ret)
    if ret == 0 then
        sys.publish("REBOOT_SAFE")
    end
end

-- local function otaCb(result,filePath,md5,size)
--     log.info("aLiYunOta.otaCb",gCb,result,filePath,size,(type(gName) =="string") and io.fileSize(filePath) or "function")
--     downloading = false
--     local md_ret = false
--     --校验MD5
--     if result then
--         if type(gName) == "string" then
--             local calMD5 = crypto.md_file("MD5",filePath)
--             md_ret = (string.upper(calMD5) == string.upper(md5))
--             log.info("aLiYunOta.otaCb cmp1 md5",result,calMD5,md5)
--         else   
--             md_ret = false
--             -- local calMD5 = crypto.md5()
--             -- md_ret = (string.upper(calMD5) == string.upper(md5))           
--             -- log.info("aLiYunOta.otaCb cmp2 md5",result,calMD5,md5)
--         end
--     end
    

--     -- if not result then
--     if md_ret then
--         log.info("aLiYunOta.otaCb","MD5 ok")
--         sys.taskInit(function()
--             fota.init()
--             local result, isDone, cache = fota.file(otafilePath) -- 传入具体的路径
--             if true == isDone then
--                 fota_cb(0)
--             end
--         end)
--     end
    
--     -- if gCb then
--     --     gCb(result,filePath)
--     -- else        
--     --     if result then
--     --         log.info("userServiceRebootCb","ALIYUN_OTA")
--     --         rtos.reboot()
--     --     end
--     -- end 
    
-- end

--[[
函数名：upgradeStepRpt
功能  ：新固件文件下载进度上报
参数  ：
        step：1到100代表下载进度比；-2代表下载失败
        desc：描述信息，可为空或者nil
返回值：无
]]
local function upgradeStepRpt(step,desc)
    log.info("aLiYunOta.upgradeStepRpt",step,desc,sConnected)
    if sConnected then
        if step<=0 or step==100 then sys.timerStop(getPercent) end
        lastStep = step
        aliyun.publish("/ota/device/progress/"..productKey.."/"..deviceName, 1, "{\"id\":1,\"params\":{\"step\":\""..step.."\",\"desc\":\""..(desc or "").."\"}}")
    end
end

function getPercent()    
    local step
    if type(gName) == "string" then
        step = io.fileSize(gName)*100/gFileSize
    else
        step = processed*100/gFileSize
    end
    log.info("aLiYunOta.getPercent",step)
    if step~=0 and step~=lastStep then
        upgradeStepRpt(step)
    end
    sys.timerStart(getPercent,5000)
end



--[[
{
    "code": "1000",
    "data": {
        "size": 32585,
        "sign": "1dd686232ceb4ba7aa3fef18dc64af0d",
        "version": "SC-A-G01-V1.0.1",
        "signMethod": "Md5",
        "isDiff": 1,
        "url": "https://ota-cn-shanghai.iot-thing.aliyuncs.com/ota/e6d7ab96d6cd41776ccee2df6612d951/clv4vrb4s00003j8ys5amcitg.bin?auth_key=1713579131-7c9762074a764c0fb6a165d4f10aeae1-0-8be6bff82699f7d41ff8847c7f91da20",
        "md5": "1dd686232ceb4ba7aa3fef18dc64af0d"
    },
    "id": 1713494531134,
    "message": "success"
}
]]

local libfota2 = require "libfota2"
local ota_opts = {
    url = "", 
    headers = {},
    url_done = true,

    -- 合宙IOT平台的默认升级URL, 不填就是这个默认值
    -- 如果是自建的OTA服务器, 则需要填写正确的URL, 例如 http://192.168.1.5:8000/update
    -- 如果自建OTA服务器,且url包含全部参数,不需要额外添加参数, 请在url前面添加 ### 
    -- 如果不加###，则默认会上传如下参数
    -- 1. opts.version string 版本号, 默认是 BSP版本号.x.z格式
    -- 2. opts.timeout int 请求超时时间, 默认300000毫秒,单位毫秒
    -- 3. opts.project_key string 合宙IOT平台的项目key, 默认取全局变量PRODUCT_KEY. 自建服务器不用填
    -- 4. opts.imei string 设备识别码, 默认取IMEI(Cat.1模块)或WLAN MAC地址(wifi模块)或MCU唯一ID
    -- 5. opts.firmware_name string 底层版本号
    -- 请求的版本号, 合宙IOT有一套版本号体系,不传就是合宙规则, 自建服务器的话当然是自行约定版本号了
    version = ""
    -- 其他更多参数, 请查阅libfota2的文档 https://wiki.luatos.com/api/libs/libfota2.html
}

local function downloadTask(url,size,md5,ver)
    log.info("aLiYunOta.downloadTask1",downloading,url,size,md5)
    if not downloading then
        downloading = true
        gFileSize = size
        local rangeBegin,retryCnt = 0,0
        
        ota_opts.url = "###"..url
        ota_opts.headers = {["Range"]="bytes="..rangeBegin.."-"}
        
        libfota2.request(fota_cb, ota_opts)
        gName = ota_opts.dst
        sys.timerStart(getPercent,5000)
        sys.publish("LIB_ALIYUN_OTA_DOWNLOAD_BEGIN",ver)
        
    end
end


--[[
函数名：upgrade
功能  ：收到云端固件升级通知消息时的回调函数
参数  ：
        payload：消息负载（原始编码，收到的payload是什么内容，就是什么内容，没有做任何编码转换）
返回值：无
]]
function upgrade(payload)
    local result
    local jsonData,result = json.decode(payload)
    log.info("aLiYunOta.upgrade",result,payload)
    -- if  fota_start() ~=0 then 
    --     log.info("fota_start fail")
    --     sys.timerStart(verRpt,10000)
    --     return
    -- end     
    if result and jsonData.data and jsonData.data.url then
        sys.taskInit(downloadTask,jsonData.data.url,jsonData.data.size,jsonData.data.md5,jsonData.data.version)
    end
end


--[[
函数名：verRptCb
功能  ：上报固件版本号给云端后，收到PUBACK时的回调函数
参数  ：
        result：true表示上报成功，false或者nil表示失败
返回值：无
]]
-- local function verRptCb(result)
--     log.info("aLiYunOta.verRptCb",result)
--     verRpted = result
--     if not result then sys.timerStart(verRpt,20000) end
-- end

--[[
函数名：verRpt
功能  ：上报固件版本号给云端
参数  ：无
返回值：无
]]
-- function verRpt()
--     log.info("aLiYunOta.verRpt",sConnected,gVersion)
--     if sConnected then
--         aliyun.publish("/ota/device/inform/"..productKey.."/"..deviceName, 1, "{\"id\":1,\"params\":{\"version\":\""..gVersion.."\"}}",verRptCb)
--     end
-- end

-- function connectCb(result,key,name)
--     sConnected = result
    
--     if result then
--         log.info("aLiYunOta.connectCb",verRpted)
--         productKey,deviceName = key,name
--         --订阅主题
--         aliyun.subscribe({["/ota/device/upgrade/"..key.."/"..name]=0, ["/ota/device/upgrade/"..key.."/"..name]=1})
--         if not verRpted then        
--             --上报固件版本号给云端
--             verRpt()
--         end
--     else
--         sys.timerStop(verRpt)
--     end
-- end

function isDownloading()
    return downloading
end

--- 设置当前的固件版本号
-- @string version，当前固件版本号
-- @return nil
-- @usage
-- aLiYunOta.setVer("MCU_VERSION_1.0.0")
-- function setVer(version)
--     local oldVer = gVersion
--     gVersion = version
--     if verRpted and version~=oldVer then        
--         verRpted = false
--         verRpt()
--     end
-- end

--- 设置新固件保存的文件名
-- @string name，新固件下载后保存的文件名；注意此文件名并不是保存的完整路径，完整路径通过setCb设置的回调函数去获取
-- @return nil
-- @usage
-- aLiYunOta.setName("MCU_FIRMWARE.bin")
function setName(name)
    gName = name
end

--- 设置新固件下载后的回调函数
-- @function cbFnc，新固件下载后的回调函数
-- 回调函数的调用形式为：cbFnc(result,filePath)，result为下载结果，true表示成功，false或者nil表示失败；filePath为新固件文件保存的完整路径
-- @return nil
-- @usage
-- aLiYunOta.setCb(cbFnc)
function setCb(cbFnc)
    gCb = cbFnc
end

local aliyunota = {
    deviceName = deviceName,
    productKey = productKey,
    sConnected = sConnected,
    connectCb = connectCb,
    upgrade = upgrade,
    setVer = setVer,
}

return aliyunota