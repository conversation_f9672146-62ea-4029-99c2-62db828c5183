
local sys = require "sys"
local hlc668 = require "hlc668"
local v_uart = require "v_uart"
local relay = require "relay"
-- 按键功能定义
-- 1、一键匹配     -短按2次
-- 2、复位         -长按
-- 3、测试         -短按3次

-- 可以实现的动作
-- 1、长按
-- 2、短按1次
-- 3、短按2次
-- 4、短按3次

local USER_PIN = 29
local KEY_UP = 1
local KEY_DOWN = 0
local key_press_count = 0

local long_press_threshold = 5000  -- 长按阈值，单位毫秒
local short_press_interval = 1000
local keytime = 0
local key_time_run_poll = 10
local press_time = 0
local release_time = 0

local LED_ENABLE = false
local LED_STA_GPIO = 27
local LED_LINK_GPIO = 31
local gledRun = 0

-- local function len_enable()
--     if false == LED_ENABLE then
--         log.info("len_enable")
--         LED_ENABLE = true
--     else
--         log.info("len_disable")
--         LED_ENABLE = false
--     end
    
-- end


local function link_led_control(num)
    -- log.info("link_led_control")
    if 0 == num then
        gpio.setup(LED_LINK_GPIO,0)
		-- gpio.setup(LED_STA_GPIO,0)
    elseif 1 == num then
        gpio.setup(LED_LINK_GPIO,1)
		-- gpio.setup(LED_STA_GPIO,0)
    else
        -- log.info("link_led_control","error")
    end
end

sys.subscribe("LINK_LED",link_led_control)



--登录成功闪烁
local function Ledtask()
	while true do
		if gledRun == 0 and true == LED_ENABLE then 
            -- gpio.setup(LED_LINK_GPIO,0)
			-- gpio.setup(LED_STA_GPIO,1)
			gledRun = 1
        elseif gledRun == 1 and true == LED_ENABLE then
            -- gpio.setup(LED_LINK_GPIO,1)
			-- gpio.setup(LED_STA_GPIO,0)
			gledRun = 0
        else
            -- gpio.setup(LED_STA_GPIO,0)
            -- gpio.setup(LED_LINK_GPIO,1)
            gledRun = 0
		end
		sys.wait(1000)
	end
end


local key_func = {
    function ()
        -- sys.publish("REBOOT_SAFE")
        relay.toggle()
    end,
    v_uart.set_status,
    bsp_ir_chip_one_step_match,
    -- bsp_ir_get_chip_version,
    -- function ()
        
        
    -- end
}



local function key_call_back(long_times,short_times)
    if 1 == long_times then
        log.info("key_call_back ","long press")
        sys.taskInit(key_func[1])
    elseif 1 == short_times then
        log.info("key_call_back ","short press 1")
        sys.taskInit(key_func[2])
    elseif 2 == short_times then
        log.info("key_call_back ", "short press 2")
        sys.taskInit(key_func[3],1)
    -- elseif 3 == short_times then
    --     log.info("key_call_back ", "short press 3")
    --     sys.taskInit(key_func[4])
    end
    press_time = 0
    release_time = 0
    key_press_count = 0
end

-- sys.subscribe("KEY_EVENT",key_call_back)



local function key_time_run()
    keytime = keytime + 1
    if release_time >= press_time then
        if  key_press_count ~= 0  and  keytime - release_time > short_press_interval/key_time_run_poll then
            log.info("key_time_run","key_press_count",key_press_count)
            -- sys.publish("KEY_EVENT",0,key_press_count)
            key_call_back(0,key_press_count)
        end
    else 
        if key_press_count ~= 0  and  keytime - press_time > short_press_interval/key_time_run_poll then
            log.info("key_time_run","key_press_count",key_press_count)
            -- sys.publish("KEY_EVENT",0,key_press_count)
            key_call_back(0,key_press_count)
        end
    end
end


local function key_irq()
    -- log.info("key irq")
    local key_level = gpio.get(USER_PIN)
    if key_level == KEY_UP then
        -- log.info("key up")
        release_time = keytime
        -- log.info("release_time",release_time,press_time)
        if release_time - press_time > long_press_threshold/key_time_run_poll and key_press_count == 0 then
            -- sys.publish("KEY_EVENT",1,0)
            key_call_back(1,0)
        elseif release_time - press_time <= short_press_interval/key_time_run_poll then
            key_press_count = key_press_count + 1
        elseif release_time - press_time > short_press_interval/key_time_run_poll
            and release_time - press_time <= long_press_threshold/key_time_run_poll  then
            key_press_count = key_press_count + 1
            -- sys.publish("KEY_EVENT",0,key_press_count)
            key_call_back(0,key_press_count)
        end
    elseif key_level == KEY_DOWN then
        -- log.info("key down")
        press_time = keytime 
        -- log.info("press_time",press_time)     
    end 
end

local user_level = KEY_UP
local user_level_last = KEY_UP
local function detect_edge(fun)
    -- 上升沿触发
    if user_level == KEY_UP and user_level_last == KEY_DOWN then
        -- log.info("key irq up")
        fun()

    -- 下降沿触发
    elseif user_level == KEY_DOWN and user_level_last == KEY_UP then
        -- log.info("key irq down")
        fun()
    end
    user_level_last = user_level
    user_level = gpio.get(USER_PIN)
end

local function init()
    gpio.setup(USER_PIN,
                nil,
                gpio.PULLUP,
                gpio.BOTH)  --设置按键引脚
    gpio.debounce(USER_PIN, 1, 1)
    user_level = gpio.get(USER_PIN)
    user_level_last = user_level

    -- LED初始化
    gpio.setup(LED_LINK_GPIO,0)
	-- gpio.setup(LED_STA_GPIO,0)

    sys.taskInit(function ()
        while true do
            detect_edge(key_irq)
            key_time_run()
            sys.wait(key_time_run_poll)
        end
    end)

    -- v_uart.set_status()

end 


local netLed = require ("netLed")

local LEDA = gpio.setup(LED_STA_GPIO,1,gpio.PULLUP) --LED引脚判断赋值结束
sys.taskInit(function()

    -- sys.wait(5000)--延时5秒等待网络注册
    -- log.info("mobile.status()", mobile.status())
    -- 
    -- netLed.taskLte(LEDA)
    netLed.setBlinkTime("FLYMODE",1000,500)  --表示飞行模式工作状态下,指示灯闪烁规律为: 亮1秒,灭8.5秒
    netLed.setBlinkTime("SIMERR",2000,2000)
    netLed.setBlinkTime("IDLE",1000,0)
    netLed.setBlinkTime("GSM",500,500)
    netLed.setBlinkTime("GPRS",100,100)
    netLed.setBlinkTime("SCK",1000,1000)
    netLed.setup(true,LED_STA_GPIO,0)
    --   while true do
    --         if mobile.status() == 1 then--已注册
    --             sys.wait(688)
    --     -- 呼吸灯
    --             netLed.setupBreateLed(LEDA)
    --         end
    --    end
end)


local key = {
    init = init,
    Ledtask = Ledtask,
}

return key
