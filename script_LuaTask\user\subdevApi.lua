--[[

   ┏┓　　　┏┓
 ┏┛┻━━━┛┻┓
 ┃　　　　　　　┃
 ┃　　　━　　　┃
 ┃　＞　　　＜　┃
 ┃　　　　　　　┃
 ┃...　⌒　...　┃
 ┃　　　　　　　┃
 ┗━┓　　　┏━┛
     ┃　　　┃　
     ┃　　　┃
     ┃　　　┃
     ┃　　　┃  神兽保佑
     ┃　　　┃  代码无bug　　
     ┃　　　┃
     ┃　　　┗━━━┓
     ┃　　　　　　　┣┓
     ┃　　　　　　　┏┛
     ┗┓┓┏━┳┓┏┛
       ┃┫┫　┃┫┫
       ┗┻┛　┗┻┛
--]]


local aliyun = require "aliyun"
local equips = require "equipManager"
local aliyunota = require"aliyunota"
local ir_chip = require "hlc668"
local e_chip = require "bl6552"
local ac = require "ac_conditioner"
local key       = require "key"
local sys = require "sys"
local ntc = require "ntc"
local at_exec = require "at_exec"


local CYCLE = 60

local START_FLAG = 0
local fast_up_times = 0
local last_up_time = 0


--硬件类型
HardType = {
    {
        ["attrKey"] = "HardType",
        ["attrValue"] = _G.PROJECT
    }
}


--阿里云客户端是否处于连接状态
sConnected = false
--消息发布次数
local publishCnt = 1
local thing_request_id = 0
--主题table
local topics = {}
-- local eventTopics = {}
local services = {"CMD", "Reboot"}

--配置标记
local GATEWAY_CONFIG_FLAG = 0
local EQUIP_CONFIG_FLAG = 0
local EQUIP_DATA_FLAG = 0

--命令响应相关的全局变量
local CMDID = nil
local TASKID = nil


-- local SYS_TICK = 0

local last_store_time =  0

-- local function num2point(x)
--     return x-x%0.01
-- end

--根据自己的服务器修改以下参数
local tPara = {
    Registration = false,            --是否是预注册 已预注册为false  true or false,
    DeviceName = "",                --设备名称
    ProductKey = "",                --产品key
    ProductSecret = "",             --产品secret
    DeviceSecret = "",              --设备secret
    mqtt_host = "",                 --如果没有注册需要填写实例id，在实例详情页面
    mqtt_port = 1883,               --mqtt端口
    mqtt_isssl = true,              --是否使用ssl加密连接，true为无证书最简单的加密
    InstanceId = "",               --实例id
 }
 local err_name = "0000000000000000000"

 tPara.ProductKey = fskv.get("product_key") or "a19NCfTXMmx"
 tPara.DeviceName = fskv.get("device_name") or err_name
 tPara.ProductSecret = fskv.get("product_secret") or "BUu4dCwoqfw2fXy1"
 tPara.InstanceId = fskv.get("instance_id") or "iot-060a57j1"
 tPara.DeviceSecret = fskv.get("device_secret")
 tPara.mqtt_host = fskv.get("mqtt_host") or tPara.InstanceId..".mqtt.iothub.aliyuncs.com"
 tPara.mqtt_port = 1883
 if fskv.get("mqtt_port") ~= nil then
     tPara.mqtt_port = tonumber(fskv.get("mqtt_port"))
 end


 local socketsoftdog = sys.taskInit(function ()      -- 网络业务看门狗
    while true do
        if sys.wait(360000) ==nil then--等待30秒，没有喂狗重启
            log.info("userServiceRebootCb","网络业务逻辑看门狗重启")
            sys.publish("REBOOT_SAFE")
        end
    end
end)



-- 网关Topic初始化
local function topicsInit(productKey, deviceName)
    topics.__thing_topic_version_post = string.format("/ota/device/inform/%s/%s", productKey, deviceName)
    topics.__thing_topic_ota_get = string.format("/ota/device/upgrade/%s/%s", productKey, deviceName)
    
    topics.__thing_topic_prop_post = string.format("/sys/%s/%s/thing/event/property/post", productKey, deviceName)
    topics.__thing_topic_prop_post_reply = topics.__thing_topic_prop_post .. "_reply"
    topics.__thing_topic_prop_set = string.format("/sys/%s/%s/thing/service/property/set", productKey, deviceName)
    topics.__thing_topic_prop_set_reply = topics.__thing_topic_prop_set .. "_reply"
    topics.__thing_topic_prop_get = string.format("/sys/%s/%s/thing/service/property/get", productKey, deviceName)
    topics.__thing_topic_prop_get_reply = topics.__thing_topic_prop_get .. "_reply"
    topics.__thing_topic_update_device_info_up = string.format("/sys/%s/%s/thing/deviceinfo/update", productKey, deviceName)
    topics.__thing_topic_update_device_info_up_reply = topics.__thing_topic_update_device_info_up .. "_reply"
    topics.__thing_topic_event_post_pattern = "/sys/%s/%s/thing/event/%s/post"
    topics.__thing_topic_service_pattern = "/sys/%s/%s/thing/service/%s/post"
    topics.__mqtt_config = string.format("/%s/%s/user/push", productKey, deviceName)
    topics.__mqtt_equip_data = string.format("/%s/%s/user/data", productKey, deviceName)
    topics.__mqtt_ntp_request = string.format("/ext/ntp/%s/%s/request", productKey, deviceName)
    topics.__mqtt_ntp_response = string.format("/ext/ntp/%s/%s/response", productKey, deviceName)
    topics.__mqtt_cmd_reply = string.format("/%s/%s/user/cmdReply", productKey, deviceName)
    topics.__thing_topic_services = {}
    for k, v in pairs(services)
    do
        table.insert(topics.__thing_topic_services,
            string.format(topics.__thing_topic_service_pattern, productKey, deviceName, v))
    end
end





--生成请求ID
local function thingGetRequestId()
    thing_request_id = thing_request_id + 1
    return thing_request_id
end


--[[
函数名：publishCb
功能  ：发布成功的回调
参数  ：
para：调用mqttclient:publish时传入的para
result：true表示发布成功，false或者nil表示失败
]]
local function publishCb(result, para)
    -- log.info("publishCb", para, result)
    publishCnt = publishCnt + 1
end

--响应服务调用
local function thingAnswerService(identifier, request_id, code, data)
    local response = {
        ["id"] = request_id,
        ["code"] = code,
        ["data"] = data
    }
    local service_reply_topic = string.format(topics.__thing_topic_service_pattern, tPara.ProductKey, tPara.DeviceName, identifier) .. "_reply"
    aliyun.publish(service_reply_topic, 1, json.encode(response), publishCb, "publishTest".. publishCnt)
end

-- --属性获取处理函数
-- local function userPropertyGetEventHandle()
-- end

-- --节点命令处理函数
-- local function userServiceCmdCb()
-- end

--设备复位回调函数
local function userServiceRebootCb()
    log.info("userServiceRebootCb","用户软件重启")
    sys.publish("REBOOT_SAFE")
end


--[[
函数名：subscribeSysTopic
功能  ：网关订阅系统消息
参数  ：无
返回值：无
]]
local function subscribeSysTopic()
    local subscribe_sys_topics = {}
    table.insert(subscribe_sys_topics, {topics.__thing_topic_prop_set, 0})
    table.insert(subscribe_sys_topics, {topics.__thing_topic_prop_get, 0})
    -- table.insert(subscribe_sys_topics, {topics.__thing_topic_raw_down, 0})
    table.insert(subscribe_sys_topics, {topics.__thing_topic_prop_post_reply, 0})
    table.insert(subscribe_sys_topics, {topics.__thing_topic_update_device_info_up_reply, 0})
    table.insert(subscribe_sys_topics, {topics.__mqtt_config, 0})
    table.insert(subscribe_sys_topics, {topics.__mqtt_ntp_response, 0})
    table.insert(subscribe_sys_topics, {topics.__thing_topic_ota_get, 0})
    for k, v in pairs(topics.__thing_topic_services)
    do
        table.insert(subscribe_sys_topics, {v, 0})
    end
    for k, v in pairs(subscribe_sys_topics)
    do
        -- log.info("forwaytech", v[1], v[2])
        aliyun.subscribe(v[1], v[2])
    end
end


--网关更新标签
local function thingUpdateTags(tags)
    local topic = topics.__thing_topic_update_device_info_up
    local request_params = tags
    local request_id = thingGetRequestId()
    local request = {
        ["id"] = request_id,
        ["version"] = "1.0",
        ["params"] = request_params,
        ["method"] = "thing.deviceinfo.update"
    }
    aliyun.publish(topic, 1, json.encode(request), publishCb, "publishTest".. publishCnt)
end


--[[
函数名：thingPostProperty
功能  ：属性上报
参数  ：@string property_data,属性数据
返回值：无
]]
local function thingPostProperty(property_data)
    local request_params = property_data
    local request_id = thingGetRequestId()
    local request = {
        ["id"] = request_id,
        ["version"] = "1.0",
        ["params"] = request_params,
        ["method"] = "thing.event.property.post"
    }
    aliyun.publish(topics.__thing_topic_prop_post, 1, json.encode(request), publishCb, "publishTest".. publishCnt)
end

local function gatewayPostVersion()
    local gVersion = PROJECT.."-V"..VERSION
    local request_id = thingGetRequestId()
    local request = {
        ["id"] = request_id,
        ["params"] = {
            ["version"] = gVersion
        }
    }
    aliyun.publish(topics.__thing_topic_version_post, 1, json.encode(request), publishCb, "gatewayPostVersion ".. publishCnt)
end

--[[
函数名：gatewayPostBaseInfo
功能  ：属性上报,包含第三方平台属性
参数  ：无
返回值：无
]]
local function gatewayPostBaseInfo()
    local request_params = {
        ["ICCID"] = mobile.iccid(),
        ["IMEI"] = mobile.imei(),
        ["SQ"] = tostring(mobile.csq()),
    }
    -- log.info("gatewayPostBaseInfo", "payload:", json.encode(request_params))
    if sConnected then
        thingPostProperty(request_params)
    end
end

-- 子设备上报属性
local function equipPostProperty(property_data)
    local payload = json.encode(property_data)
    -- log.info("equipPostProperty", payload)
    aliyun.publish(topics.__mqtt_equip_data, 1, payload, publishCb, "equipPostProperty".. publishCnt)
end


--请求时间同步
local function gatewayRequestNtp()
    local request_params = {
        ["deviceSendTime"] = tostring(os.time()*1000)
    }    
    local payload = json.encode(request_params)
    -- log.info("gatewayRequestNtp", "request send")
    aliyun.publish(topics.__mqtt_ntp_request, 0, payload, publishCb, "gatewayRequestNtp")
end

--子设备上报命令响应
local function equipCmdReply(ret)
    if CMDID ~= nil and TASKID ~= nil then
        local reply = {
            ["cmdId"] = CMDID,
            ["taskId"] = TASKID,
            ["result"] = tostring(ret)
        }
        aliyun.publish(topics.__mqtt_cmd_reply, 1, json.encode(reply),  publishCb, "equipCmdReply" .. publishCnt)
    end
end


--请求配置
local function requestCloudConfig(productKey, deviceName, method)
    local topic = string.format("/%s/%s/user/request", productKey, deviceName)
    local request = {}
    request["method"] = method
    request["productKey"] = productKey
    request["deviceName"] = deviceName
    request["time"] = tostring(math.floor(os.time()/1000))
    aliyun.publish(topic, 1, json.encode(request), publishCb, "requestCloudConfig:" .. method)
end


--[[
函数名：thingPostProperty
功能  ：AT结果上报
参数  ：@string property_data,属性数据
返回值：无
]]

local function ATcmdReply(data)
    local request_params = {
        ["AT"] = data,
    }
    thingPostProperty(request_params)
end

-- MQTT 虚拟AT处理
local function app_procmd_by_mqtt(str_recv)
    -- local flag_handled, str_rsp = at_exec.app_procmd(str_recv)
    local str_rsp = at_exec.app_procmd(str_recv)
    if str_rsp ~="" then
        ATcmdReply(str_rsp)
    end

end

--属性设置处理函数
local function userPropertySetEventHandle(params)
    if params["AT"] then
        sys.taskInit(function()
            app_procmd_by_mqtt(params["AT"])
            sys.wait(100)
        end)
    end
end


-- 命令格式{"cmdMethod":"gatewayCmd","cmdName":"reboot"}
--指令调用回调函数
local function userServiceRequestEventHandle(params)
    -- log.info("forwaytech", params)
    local identifier = ""
    local method = params["method"]
    if string.find(method, "CMD") then
        identifier = "CMD"
        log.info("forwaytech", "CMD set")
        local cmdMethod = params["params"]["cmdMethod"]
        if cmdMethod == "gatewayCmd" then
            local cmdName = params["params"]["cmdName"]
            local cmdParam = 1
            if nil ~= params["params"]["cmdParam"] then
                cmdParam = tonumber(params["params"]["cmdParam"])
            else
                log.info("forwaytech", "Reboot set")
            end
            if cmdName == "reboot" then 
                log.info("forwaytech", "Reboot set")
                userServiceRebootCb()
            elseif cmdName == "reset" then
                userServiceRebootCb()
            elseif cmdName == "restore" then
                reset_params(cmdParam)
                sys.publish("REBOOT_SAFE")
            end
        elseif cmdMethod == "equipCmd" then
            log.info("forwaytech", "equipCmd")
            CMDID = params["params"]["cmdId"]
            TASKID = params["params"]["taskId"]
            log.info("forwaytechCMDID", CMDID)
            log.info("forwaytechTASKID", TASKID)
            sys.taskInit(equips.equip_proc_command, params["params"])
        end
    else
        log.info("forwaytech", "unknown service")
    end
    thingAnswerService(identifier, thingGetRequestId(), 200, {})
end




local function update_temp_and_rssi()
    --读取温湿度
    ac_status.box_temp.val = ntc.get_temp_internal()
    ac_status.indoor_temp.val = ntc.get_temp_external()
    ac_status.rssi.val = mobile.csq()

end



--更新数据
local function update_ac_data()

    local now_time = math.floor(os.time())
    if last_store_time == 0 then
        last_store_time = now_time
    end
    local n = now_time - last_store_time
    last_store_time = now_time

    ac.get_data()
    
    if ac_status.p.val > ac_status.c_run_power.val then  
        ac_status.runningstatus.val = 1
        ac_status.comp_status.val = 1
        ac_status.comp_time.val = ac_status.comp_time.val + n/3600
        ac_status.comp_running_time.val = ac_status.comp_running_time.val + n/60        
    elseif ac_status.p.val > ac_status.run_power.val then
        ac_status.comp_running_time.val = 0        
        ac_status.runningstatus.val = 1
        ac_status.comp_status.val = 0
    else 
        ac_status.comp_running_time.val = 0        
        ac_status.runningstatus.val = 0
        ac_status.comp_status.val = 0
    end

    ac_status.power_status.val = 1
        
    -- using_time 定义为开机的总时长
    if ac_status.runningstatus.val == 1 then
        ac_status.using_time.val = ac_status.using_time.val + n/3600
    end
    
   return 0
end


local function gatewayPostProperty()
    local upload_data = {}

    for k,v in pairs(ac_status) do
        upload_data[k] = v.val
    end


    local property_data = {
        ["resource"] = tostring(json.encode(upload_data))
    }
    if sConnected then
        thingPostProperty(property_data)
    end
end

sys.subscribe("UPLOAD_RESOURCE",gatewayPostProperty)



--[[
@description: 保存一个数据到flash
@return {*}
--]]
local function save_one_data(k,v)
    if nil ~= ac_status[k] then
        ac_status[k].val = v
    end
    if v ~= fskv.get(k) then
        fskv.set( k, v)
        log.info("save_one_data", k,v)
    end
end

sys.subscribe("SAVE_ONE_DATA", save_one_data)

--计算使用时间，并且保存参数
local function cal_times_and_store_param()

    local ac_data = {
        ["comp_time"]=ac_status.comp_time.val,
        ["using_time"]=ac_status.using_time.val
    }

    for k,v  in pairs(ac_data) do
        save_one_data(k,v)
        -- log.info("save data", k,v)
    end
    log.info("cal_times_and_store_param", "store ac_data")
end





--向云端发送设备数据
local function upload_data()
    local nodedata = {}

    for k, v in pairs(ac_status) do
        nodedata[v.index] = v.val
    end

    equips.equips_update_properties(nodedata)
end

sys.subscribe("UPLOAD_DATA",upload_data)


local function update_fast_up_times(times)  
    fast_up_times = times
end

-- 定时上传任务
local function upload_task(interval)
    while true do 
        local change_flag = 0
        local last_run_status = ac_status.runningstatus.val

        if update_ac_data() == 0 then    --更新数据
            if last_run_status ~= ac_status.runningstatus.val then   -- 开关状态发生变更
                change_flag = 1
            end

            local now_time = math.floor(os.time())
            local t1 = interval
            if fast_up_times > 0 then
                t1 = 3
            end

            if (now_time - last_up_time > t1) or (change_flag == 1) then
                update_temp_and_rssi()
                gatewayPostProperty()
                sys.publish("UPLOAD_DATA")
                -- print(string.format("now time %d t1 %d  last_uptime %d", now_time, t1, last_up_time))
                last_up_time = now_time
                if fast_up_times > 0 then 
                    fast_up_times = fast_up_times -1
                end
            end
        end
        sys.wait(1000)
    end   
end

--配置保存函数
local function userSaveCloudConfigHandle(params)
    local method = params["method"]
    log.info("forwaytech", method)
    if method == "gatewayConfig" then
        if params["gateway"] then   
            if params["gateway"]['dataCycle'] then   
                CYCLE = tonumber(params["gateway"]['dataCycle'])
                GATEWAY_CONFIG_FLAG = 1
            end
        end
    elseif method == "equipConfig" then
        if EQUIP_CONFIG_FLAG == 0 then
            if params["equips"] ~= nil then
                equips.equips_add(params["equips"])
                log.info("forwaytech", "equips save")
                EQUIP_CONFIG_FLAG = 1
                sys.subscribe("FAST_UP", update_fast_up_times)  --need updata then upload fast up times
            end
        end
    elseif method == "dataConfig" then
        if EQUIP_DATA_FLAG == 0 then
            if params["equipDatas"] ~= nil then
                equips.equips_data_to_resource(params["equipDatas"])
                log.info("forwaytech", "equips data save")
                EQUIP_DATA_FLAG = 1
                sys.subscribe("FAST_UP", update_fast_up_times)
            end
        end
    else
        log.info("forwaytech", "unknown config")
    end
end


--同步时间
local function ntp_save(params)
    if params ~= nil then
        local serverSendTime = tonumber(params["serverSendTime"])
        local serverRecvTime = tonumber(params["serverRecvTime"])
        local deviceSendTime = tonumber(params["deviceSendTime"])

        local t = math.floor((serverSendTime + serverRecvTime + os.time()*1000 - deviceSendTime )/2)
        log.info("ntp_save", t)
        local ntim = os.date("*t", math.floor(t/1000))
        -- misc.setClock(ntim)
        rtc.set(ntim)
    end 
end


-- 接收回调
local function rcvCbFnc(topic, payload)
    -- log.info("forwaytech", "testaliyun.rcvCbFnc", topic, payload)
    --根据主题进行消息的分发
    --对于某些主题，需要通过publish达到同步的效果
    log.info("debug", "rcvCbFnc\r\n")
    if string.find(topic, "CMD", 1) then
        local params, result, errinfo = json.decode(payload)
        userServiceRequestEventHandle(params)
    elseif string.find(topic, "user/push", 1) then
        log.info("forwaytech", "user/push")
        local params, result, errinfo = json.decode(payload)
        userSaveCloudConfigHandle(params)
    elseif string.find(topic, "service/property/set", 1) then
        local params, result, errinfo = json.decode(payload)
        userPropertySetEventHandle(params["params"])
    elseif string.find(topic, "property/post_reply", 1) then
        --业务喂狗,如果不通，重启
        coroutine.resume(socketsoftdog,"feed")
        log.info("网络业务成功喂狗")
    elseif string.find(topic, "ext/ntp", 1) then
        log.info("forwaytech", "ext/ntp")
        local params, result, errinfo = json.decode(payload)
        ntp_save(params)
    elseif string.find(topic, "/ota/device/upgrade/", 1) then
        log.info("aliyunota", "ota upgrade")
        aliyunota.upgrade(payload)
    else
        log.info("forwaytech", "unknown topic")
    end
end


--请求云端配置
local function requestCloudConfigs(productKey, deviceName)
    log.info("forwaytech", "requestCloudConfig")
    while (GATEWAY_CONFIG_FLAG == 0)
    do
        requestCloudConfig(productKey, deviceName, "gatewayConfig")
        sys.wait(2000)
    end
    while (EQUIP_CONFIG_FLAG == 0)
    do
        requestCloudConfig(productKey, deviceName, "equipConfig")
        sys.wait(2000)
    end
    
    while (EQUIP_DATA_FLAG == 0)
    do
        requestCloudConfig(productKey, deviceName, "dataConfig")
        sys.wait(2000)
    end
    
    
    --订阅设备属性上报
    sys.subscribe("EQUIPDATA", equipPostProperty)
    sys.subscribe("CMDREPLY", equipCmdReply)
    sys.taskInit(key.Ledtask)
    sys.taskInit(upload_task, CYCLE)
    last_store_time = math.floor(os.time())
end

--- 连接结果的处理函数
-- @bool result，连接结果，true表示连接成功，false或者nil表示连接失败
local function connectCbFnc(result)
    log.info("forwaytech", "testaliyun.connectCbFnc", result)
    
    sConnected = result
    if result then
        --初始化主题
        topicsInit(tPara.ProductKey, tPara.DeviceName)
        --订阅主题，不需要考虑订阅结果，如果订阅失败，aliyun库中会自动重连
        subscribeSysTopic()
        --注册数据接收的处理函数
        aliyun.on("receive", rcvCbFnc)
        
        --上传网关基础信息
        gatewayPostBaseInfo()

        -- 上传程序版本信息
        gatewayPostVersion()


        --更新网关标签
        thingUpdateTags(HardType)

        --请求时间戳
        gatewayRequestNtp()

        --设置升级信息
        aliyunota.deviceName = tPara.DeviceName
        aliyunota.productKey = tPara.ProductKey
        aliyunota.sConnected = sConnected
        -- aliyunota.setVer(PROJECT.."-V"..VERSION)
        -- aliyunota.connectCb(sConnected,tPara.ProductKey,tPara.DeviceName)


        --请求网关配置
        if START_FLAG == 0 then
            sys.taskInit(requestCloudConfigs, tPara.ProductKey, tPara.DeviceName)
            START_FALG = 1
        end
        --errDump.appendErr("connect aliyun success "..getProductKey().."  "..getDeviceName())
    else
        log.info("userServiceRebootCb","连接失败 等待重连")

       
    end
end


--网关发布信号
local function GateWayPostRSSI()
    local rssi = tostring(mobile.csq())
    local property_data = {
        ["SQ"] = rssi,
        ["CPU_TEMP"] = ntc.get_cpu_temp(),
        
    }
    if sConnected then
        thingPostProperty(property_data)
    end
end


--网关发布信号
local function ntp_sync()
    if sConnected then
        gatewayRequestNtp()
    end
end

local function save_ir_data()
    sys.wait(10)
    save_one_data("fan", ac_status.fan.val)
    save_one_data("fan_type", ac_status.fan_type.val)
    save_one_data("mode", ac_status.mode.val)
    save_one_data("temp", ac_status.temp.val)
end

local function save_e_data()
    sys.wait(10)
    save_one_data("ep", ac_status.ep.val)
    save_one_data("ep_a", ac_status.ep_a.val)
    save_one_data("ep_b", ac_status.ep_b.val)
    save_one_data("ep_c", ac_status.ep_c.val)
    sys.wait(10)
    save_one_data("ep_init", ac_status.ep_init.val)
    save_one_data("ep_init_a", ac_status.ep_init_a.val)
    save_one_data("ep_init_b", ac_status.ep_init_b.val)
    save_one_data("ep_init_c", ac_status.ep_init_c.val)
end

local function reboot_safe()
    sys.taskInit(function ()
        cal_times_and_store_param()
        save_ir_data()
        save_e_data()
        sys.wait(5000)
        rtos.reboot()
    end)
end


sys.subscribe("REBOOT_SAFE", reboot_safe)




--初始化
aliyun.on("connect",connectCbFnc)
aliyun.on("receive",rcvCbFnc)


--采用一机一密认证方案时：
--配置：ProductKey、获取DeviceName的函数、获取DeviceSecret的函数；其中aliyun.setup中的第二个参数必须传入nil
sys.taskInit(
    function()
        ac.data_recover()
        ac.init()


	    local failed_times = fskv.get("failed_times")
        sys.taskInit(function()
            while true do
                sys.wait(15*60*1000)
                if not sConnected then
                    if nil == failed_times then
                        failed_times = 0
                    end
                    failed_times = failed_times + 1
                    fskv.set("failed_times", failed_times)
                    sys.publish("REBOOT_SAFE")
                end
            end
        end)
        sys.taskInit(function()
            while err_name == tPara.DeviceName do
                log.info("aliyun.setup"," DeviceName error")
                sys.wait(1000)
            end
            if failed_times == 10 then
            	failed_times = 0
            	fskv.set("failed_times", failed_times)
            	aliyun.setup(tPara,1)
            else
                aliyun.setup(tPara,nil)
            end
        end)
        sys.taskInit(function()
            while true do
                local last_sConnected = sConnected
                sys.wait(1000)
                if sConnected ~= last_sConnected then
                    if sConnected then
                        -- log.info("aliyun.connect","success")
                        sys.publish("LINK_LED",1)
                    else
                        -- log.info("aliyun.connect","failed")
                        sys.publish("LINK_LED",0)
                    end
                    last_sConnected = sConnected
                end
            end
        end)
    end
)




local softwareDogCo = sys.taskInit(function()
    while true do
        if sys.wait(400000) == nil then --连续400s没有喂狗，触发重启
            log.info("softwareDogCo","feed dog fail")
            sys.publish("REBOOT_SAFE")
        -- elseif  not sConnected then
        --     log.info("softwareDogCo","aliyun not connected, reboot")
        --     sys.publish("REBOOT_SAFE")
        end
    end
end)
    
sys.timerLoopStart(function()
    coroutine.resume(softwareDogCo,"feed")
end, 100000)                        -- 100s喂狗一次



--三分钟上报一次4G信号强度
sys.timerLoopStart(GateWayPostRSSI, 180000)
-- sys.timerLoopStart(GateWayPostRSSI, 20000)

--60分钟保存一次
sys.timerLoopStart(cal_times_and_store_param, 60*60*1000)

--每小时进行一些时间同步
sys.timerLoopStart(ntp_sync, 3600000)

