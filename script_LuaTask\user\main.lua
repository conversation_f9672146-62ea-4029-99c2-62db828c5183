--[[
Description: 
Version: V1.0.0
Autor: mingwangyang
Date: 2024-03-30 17:45:39
LastEditors: mingwangyang
LastEditTime: 2024-04-20 14:35:20
--]]

PROJECT = "SC-A-G03"
VERSION = "*******"
local sys = require "sys"

-- ****************************业务代码***********************
-- 初始化kv数据库
fskv.init()
log.info("fskv", "init complete")

sys.taskInit(function()
    local count = 1
    while 1 do
        sys.wait(1000)
        -- log.info("luatos", "hi", count, os.date())
        -- lua内存
        log.info("lua", rtos.meminfo())
        -- sys内存
        -- log.info("sys", rtos.meminfo("sys"))
        
        -- log.info("fskv status:", fskv.status())
        
        -- count = count + 1
    end
end)

require "subdevApi"

-- ****************************业务代码***********************


-- sys.taskInit(function()
--     while true do
--         log.info("imei", mobile.imei())
--         log.info("imsi", mobile.imsi())
--         local sn = mobile.sn()
--         if sn then
--             log.info("sn",   sn:toHex())
--         end
--         log.info("muid", mobile.muid())
--         log.info("iccid", mobile.iccid())
--         log.info("csq", mobile.csq())
--         log.info("rssi", mobile.rssi())
--         log.info("rsrq", mobile.rsrq())
--         log.info("rsrp", mobile.rsrp())
--         log.info("snr", mobile.snr())
--         log.info("simid", mobile.simid())
--         log.info("eci", mobile.eci())
--         log.info("tac", mobile.tac())
--         log.info("enbid", mobile.enbid())
--         log.info("enbid_type",type(mobile.enbid()))
--         sys.wait(10000)
--     end
-- end)



--  ********************** 测试使用 *********************

log.info("main", "start ", PROJECT, VERSION)

-- -- ********************** 合宙升级平台使用 *********************

local libfota2 = require "libfota2"

PRODUCT_KEY = "QdfHuqBVxZNaIvSDTDQBqklxNc7SZuXW"
-- Air780E的AT固件默认会为开机键防抖, 导致部分用户刷机很麻烦
if rtos.bsp() == "EC618" and pm and pm.PWK_MODE then
    pm.power(pm.PWK_MODE, false)
end



local function fota_cb(ret)
    log.info("fota", ret)
    if ret == 0 then
        sys.publish("REBOOT_SAFE")
    end
end


-- 使用合宙iot平台进行升级, 支持自定义参数, 也可以不配置
local ota_opts = {
    -- 合宙IOT平台的默认升级URL, 不填就是这个默认值
    -- 如果是自建的OTA服务器, 则需要填写正确的URL, 例如 http://192.168.1.5:8000/update
    -- 如果自建OTA服务器,且url包含全部参数,不需要额外添加参数, 请在url前面添加 ### 
    -- url="http://iot.openluat.com/api/site/firmware_upgrade",
    -- 请求的版本号, 合宙IOT有一套版本号体系,不传就是合宙规则, 自建服务器的话当然是自行约定版本号了
    -- version=_G.VERSION,
    -- 其他更多参数, 请查阅libfota2的文档 https://wiki.luatos.com/api/libs/libfota2.html
}

libfota2.request(fota_cb, ota_opts)
sys.timerLoopStart(libfota2.request, 4*3600000, fota_cb, ota_opts)


-- ********************** 合宙平台使用 *********************



-- 自己的平台升级测试
-- local ota_url = 'http://193.112.62.121:7999/SC-A-G01_V101.bin'
-- local function fota_cb(ret)
--     log.info("fota", ret)
--     if ret == 0 then
--         sys.publish("REBOOT_SAFE")
--     end
-- end
-- libfota.request(fota_cb,ota_url)
-- sys.timerLoopStart(libfota.request, 3600000, fota_cb, ota_url)








-- 用户代码已结束---------------------------------------------
-- 结尾总是这一句


sys.run()

