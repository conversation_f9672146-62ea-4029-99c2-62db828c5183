--[[
Description: 用于AT指令的串口
Version: V1.0.0
Autor: <PERSON>
Date: 2024-07-21 15:02:00
LastEditors: <PERSON>
LastEditTime: 2024-07-21 15:02:00
--]]


local sys = require "sys"
local at_exec = require "at_exec"
local V_UART_TX_PIN = 17
local V_UART_RX_PIN = 16

local V_UART_VCC_PIN = 2

local V_UART_CLOSE = 0
local V_UART_OPEN = 1
local v_uart_status = V_UART_CLOSE   --0关闭（close和未初始化都为关闭），1打开
local v_uart_set_status = V_UART_CLOSE


local uartid = 99

local function resouce()     

        return V_UART_TX_PIN,0,V_UART_RX_PIN,2,9600,0,-10

end
local tx_pin,tx_timer,rx_pin,rx_timer,br,tx_adjust,rx_adjust = resouce() 


-- at处理入口
local function at_proc(str_recv)
    local str_rsp = at_exec.app_procmd(str_recv)
    if str_rsp ~=nil then
        uart.write(uartid, str_rsp)
    end
end

sys.subscribe("at", function (str_recv)
    sys.taskInit(at_proc,str_recv)
end)

local function rxDone_callback(id, len)
    local s = ""
    repeat
        s = uart.read(id, len)
        if s and #s > 0 then -- #s 是取字符串的长度
            -- log.info("uart", "receive", id, #s, s)
            sys.publish("at", s)
        end
    until s == ""
end

local function txDone_callback(id)
    -- log.info("uart", "sent", id)
end

-- 需要回车换行，不然不识别
-- local function read(id, len)
--     local data = uart.read(id, len)
--     if not data or string.len(data) == 0 then 
--         return
--     else
--         log.info("testUart.read bin", string.len(data) , data)
--         log.info("testUart.read hex", string.len(data) , data:toHex())
--         uart.write(id,data)
--     end
-- end


-- local function writeOk(id)
--     -- log.info("testUart.writeOk")
-- end


local function v_uart_power_on()
    gpio.setup(V_UART_VCC_PIN,0,gpio.PUDOWN)
end

local function v_uart_power_off()
    gpio.setup(V_UART_VCC_PIN,1,gpio.PULLUP)
end

--如果初始化串口，引脚就不能被控制，所以不需要pin_on
local function v_uart_pin_off()
    gpio.setup(V_UART_RX_PIN,0)
    gpio.setup(V_UART_TX_PIN,0)
end

--[[
@description: 关闭蓝牙及其引脚
@return {*}
--]]
local function v_uart_close()
    uart.close(uartid)
    v_uart_power_off()
    v_uart_pin_off()
end




--[[
@description: 打开蓝牙电源并初始化串口
@return {*}
--]]
local function v_uart_open()
    v_uart_power_on()
    -- log.info("uartid",uartid)
    uartid = uart.createSoft(tx_pin,tx_timer,rx_pin,rx_timer,tx_adjust,rx_adjust)
    --初始化
    local result = uart.setup(
        uartid,--串口id
        br,--软件串口波特率根据平台的软硬件配置有不同的极限
        8,--数据位
        1,--停止位
        uart.None
    )
    -- 设置串口接收回调函数
    uart.on(uartid,"receive",rxDone_callback)
    -- 设置串口发送回调函数
    uart.on(uartid, "sent", txDone_callback)
    
end

--切换串口的状态
local function  v_uart_status_control()
    -- log.info("v_uart_status_control",v_uart_set_status)
    if V_UART_CLOSE == v_uart_set_status then
        v_uart_set_status = V_UART_OPEN
        v_uart_open()
    elseif  V_UART_OPEN == v_uart_set_status then
        v_uart_set_status = V_UART_CLOSE
        v_uart_close()
        -- log.info("v_uart_test","uart close")
    else
        v_uart_set_status = V_UART_CLOSE
    end
    return true
end





-- local function v_uart_send_test()
--     while true do
--         if V_UART_OPEN == v_uart_status then
--             -- uart.write(uartid,"1234567890")
            
--         end
--         sys.wait(1000)
--     end
-- end

-- local function v_uart_test()
--     while true do
--         if v_uart_set_status ~= v_uart_status then
--             if V_UART_CLOSE == v_uart_set_status then
--                 v_uart_close()
--                 log.info("v_uart_test","uart close")
--             elseif V_UART_OPEN == v_uart_set_status then
--                 v_uart_open()
--                 log.info("v_uart_test","uart open")
--             end
--             v_uart_status = v_uart_set_status
--         end
--         sys.wait(100)
--     end
    
-- end

-- sys.taskInit(sendTask)
-- sys.taskInit(v_uart_send_test)
-- sys.taskInit(v_uart_test)

v_uart = {
    v_uartid = uartid,
    get_status = v_uart_status,
    -- v_uart_open = v_uart_open,
    -- v_uart_close = v_uart_close,
    set_status = v_uart_status_control,

}

return v_uart


